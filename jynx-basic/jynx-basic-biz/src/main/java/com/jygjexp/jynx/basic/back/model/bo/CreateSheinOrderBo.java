package com.jygjexp.jynx.basic.back.model.bo;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 希音订单参数
 */
@Data
public class CreateSheinOrderBo {

    // 单号
    @NotBlank(message = "logiNo不能为空")
    private String logiNo;

    // 自定义编号
    @NotBlank(message = "customNo不能为空")
    private String customNo;

    // 国家
    @NotBlank(message = "country不能为空")
    private String country;

    // 省份
    @NotBlank(message = "province不能为空")
    private String province;

    // 城市
    @NotBlank(message = "city不能为空")
    private String city;

    // 地址
    @NotBlank(message = "address不能为空")
    private String address;

    // 邮政编码
    @NotBlank(message = "zip不能为空")
    private String zip;

    // 收件人
    @NotBlank(message = "consignee不能为空")
    private String consignee;

    //客户名称
    private String customerName;

    // 发件国家
    private String shipperCountryCode;

    // 发件省
    private String shipperProvince;

    // 发件城市
    private String shipperCity;

    // 发件地址
    private String shipperAddress;

    // 发件邮编
    private String shipperPostcode;

    // 发件电话
    private String shipperPhone;

    // 手机号码
    @NotBlank(message = "mobile不能为空")
    private String mobile;

    // 重量
    @NotNull(message = "weight不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "weight必须大于0")
    private BigDecimal weight;

    // 价格
    @NotNull(message = "price不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "price必须大于0")
    private BigDecimal price;

    // 下单时间
    @NotBlank(message = "orderTime不能为空")
    private String orderTime;

    // 是否退件
    @NotBlank(message = "international不能为空")
    private String international;

    // 订单类型
    @NotNull(message = "orderType不能为空")
    private Integer orderType;

    // 邮箱
    private String email;

    // 子渠道
    private String subChannel;

    // 国家ID
    private Integer countryId;

    // 省份
    private Integer provinceId;

    // 城市ID
    private Integer cityId;

    // 令牌
    private String token;

    // 邮政编码
    private String postalCode;

    // 面单路径
    private String labelPath;

    // 签名
    private String sign;

    // 随机字符串
    private String randomstr;

    // 时间戳
    private String timestamp;

}
