package com.jygjexp.jynx.basic.back.model.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jygjexp.jynx.basic.back.entity.TmsCargoInfoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 *中大件询价接口
 */
@Data
public class EstimatedPriceBo {

    /**
     * 客户ID
     */
    private Long customerId;


    /**
     * 发货人邮编
     * 示例：L6Y 5X5
     */
    private String shipperPostalCode;


    /**
     * 收货人邮编
     * 示例：V1M 2C9
     */
    private String destPostalCode;

    /**
     * 总重量（单位：千克）
     * 示例：40
     */
    private BigDecimal totalWeight;


    /**
     * 总体积（单位：立方厘米）
     * 示例：0.016
     */
    private BigDecimal totalVolume;

    /**
     * 货物类型：1=普通货物，2=危险货物
     */
    @Schema(description="货物类型：1=普通货物，2=危险货物")
    private Integer cargoType;


    /**
     * 货物信息
     */
    @TableField(exist = false)
    @Schema(description = "货物信息")
    private List<TmsCargoInfoEntity> cargoInfoEntityList;


}
