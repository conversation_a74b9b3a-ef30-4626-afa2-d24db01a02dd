package com.jygjexp.jynx.basic.back.tools;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itextpdf.text.pdf.PdfReader;
import com.jygjexp.jynx.basic.back.entity.SubOrderEntity;
import com.jygjexp.jynx.basic.back.model.vo.ZdjApiVo;
import com.jygjexp.jynx.common.core.util.R;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.time.zone.ZoneRules;
import java.util.*;

/**
 * 订单工具类
 */
public class OrderTools {

    //时间格式
    private static final DateTimeFormatter DEFAULT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    //客户单号规则
    private static final String[] CUSTOMER_LABEL_PREFIXES = {"GV", "JY", "U9999","UNI"};

    /**
     * MD5加密
     *
     * @param input 输入的字符串
     * @return 加密后的字符串
     */
    public static String getMD5(String input) {
        try {
            // 获取MD5算法实例
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 将输入字符串转换为字节数组并计算哈希值
            byte[] messageDigest = md.digest(input.getBytes());

            // 将字节数组转换为16进制格式的字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                // 每个字节转换为2位16进制数
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将list集合转字符串，中间逗号隔开
     */
    public static String buildListToString(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        return JSON.toJSONString(list).replaceAll("\"", "").replaceAll("]", "").replaceAll("\\[", "");

    }


    /**
     * 判断邮编是否为大写形式的 V6V 1K4 或 L3R 1B2
     * （允许有或没有空格，但必须是大写）
     *
     * @param input 输入的邮编
     * @return 是否匹配
     */
    public static boolean isTargetPostalCode(String input) {
        if (input == null) return false;

        // 去除所有空格
        String noSpace = input.replaceAll("\\s+", "");

        // 必须全是大写
        if (!noSpace.equals(noSpace.toUpperCase())) {
            return false;
        }

        // 匹配目标邮编（无空格形式）
        return "V6V1K4".equals(noSpace) || "L3R1B2".equals(noSpace);
    }


    //解析TMS返回结果
    public static ZdjApiVo parseTmsResult(R result) {
        Object data = result.getData();
        ObjectMapper mapper = new ObjectMapper();
        return mapper.convertValue(data, ZdjApiVo.class);
    }


    /**
     * 从URL下载文件并返回字节数组
     *
     * @param fileUrl 文件URL
     * @return 文件字节数组
     * @throws IOException 如果下载过程中发生IO异常
     */
    public static byte[] getBytesFromUrl(String fileUrl) throws IOException {
        URL url = new URL(fileUrl);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(5000);

        try (InputStream inputStream = conn.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int len;
            while ((len = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
            return baos.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }



    //是否使用客户单号作为面单
    public static boolean isCustomerLabel(String orderNumber) {
        if (orderNumber == null) return false;
        for (String prefix : CUSTOMER_LABEL_PREFIXES) {
            if (orderNumber.startsWith(prefix) && !orderNumber.startsWith("JYB")) {
                return true;
            }
        }
        return false;
    }


    //获取正确的系统子单号
    public static String safeSubstringOrder(String orderNo) {
        if (orderNo == null || orderNo.length() <= 3) {
            return "";
        }
        if (orderNo.length() <= 15) {
            return orderNo;
        }
        return orderNo.substring(0, orderNo.length() - 3);
    }


    /**
     * 将字符串时间转换为 LocalDateTime
     * @param time 需要转换的时间字符串
     * @return 转换后的 LocalDateTime 对象
     */
    public static LocalDateTime transformLocalDateTime(String time) {

        // 定义对应的时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);

        // 解析为 ZonedDateTime
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(time, formatter);

        // 转换为 LocalDateTime
        return zonedDateTime.toLocalDateTime();

    }



    /**
     * 将 LocalDateTime 格式化为字符串 yyyy-MM-dd HH:mm:ss
     * @param dateTime 需要格式化的 LocalDateTime 对象
     * @return 格式化后的字符串
     */
    public static String formatLocalDateTime(LocalDateTime dateTime) {
        DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(FORMATTER);
    }

    /**
     * 判断 bagNum 是否有重复（忽略 null）
     * @param subOrderList List<SubOrderEntity>
     * @return true 表示有重复，false 表示无重复
     */
    public static boolean hasDuplicateBagNum(List<SubOrderEntity> subOrderList) {
        Set<String> seen = new HashSet<>();

        if (subOrderList == null) return false;

        for (SubOrderEntity entity : subOrderList) {
            if (entity == null) continue;
            String bagNum = entity.getBagNum();
            if (bagNum == null) continue;
            if (!seen.add(bagNum)) {
                return true;
            }
        }

        return false;
    }

    //获取昨天开始时间
    public static LocalDateTime getYesterdayStart() {
        return LocalDateTime.now().minusDays(1).with(LocalTime.MIN);
    }

    //获取昨天结束时间
    public static LocalDateTime getYesterdayEnd() {
        return LocalDateTime.now().minusDays(1).with(LocalTime.MAX);
    }

    //获取指定格式日期时间
    public static String getFormattedDateMinusDays(String format, int day) {
        LocalDate currentDate = LocalDate.now().minusDays(day);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return currentDate.format(formatter);
    }

    //获取特定格式日期间隔
    public static Long getIntervalDays(String time) {
        // 拆分字符串
        String[] dates = time.split("-");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        LocalDate startDate = LocalDate.parse(dates[0], formatter);
        LocalDate endDate = LocalDate.parse(dates[1], formatter);
        // 计算天数差
        return ChronoUnit.DAYS.between(startDate, endDate);
    }

    //获取多伦多时区
    public static String getTimezone() {
        ZoneId torontoZone = ZoneId.of("America/Toronto");
        // 当前时间
        ZonedDateTime now = ZonedDateTime.now(torontoZone);
        // 获取时区规则
        ZoneRules rules = torontoZone.getRules();
        // 判断是否为夏令时
        boolean isDaylightSavingTime = rules.isDaylightSavings(now.toInstant());
        if (isDaylightSavingTime) {
            return "-04:00";
        } else {
            return "-05:00";
        }
    }


    //获取字符串的前三个字符
    public static String getFirstThreeChars(String str) {
        if (str == null || str.length() < 3) {
            return "";
        }
        return str.substring(0, 3);
    }


    /**
     * 判断字符串时间是否早于指定时间
     *
     * @param timeStr 字符串时间（格式 yyyy-MM-dd HH:mm:ss）
     * @param dateTime 要比较的 LocalDateTime
     * @return true 表示 timeStr 在 dateTime 之前
     */
    public static boolean isBefore(String timeStr, LocalDateTime dateTime) {
        try {
            LocalDateTime strTime = LocalDateTime.parse(timeStr, DEFAULT_FORMATTER);
            return strTime.isBefore(dateTime);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("时间格式不正确，应该为 yyyy-MM-dd HH:mm:ss", e);
        }
    }

    /**
     * 判断字符串时间是否晚于指定时间
     */
    public static boolean isAfter(String timeStr, LocalDateTime dateTime) {
        try {
            LocalDateTime strTime = LocalDateTime.parse(timeStr, DEFAULT_FORMATTER);
            return strTime.isAfter(dateTime);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("时间格式不正确，应该为 yyyy-MM-dd HH:mm:ss", e);
        }
    }


    /**
     * 去除字符串中的所有空格，包括前后和中间空格，避免空指针
     * @param input 原始字符串
     * @return 清理后的字符串（无空格）
     */
    public static String removeAllSpaces(String input) {
        if (input == null) {
            return "";
        }
        return input.replaceAll(" ", "").trim();
    }


    //保留指定小数
    public static String formatDecimal(BigDecimal decimal, int scale) {
        return Optional.ofNullable(decimal)
                .orElse(BigDecimal.ZERO)
                .setScale(scale, RoundingMode.HALF_UP)
                .toPlainString();
    }


    //字符串转BigDecimal
    public static BigDecimal safeToBigDecimal(String price) {
        if (price == null || price.trim().isEmpty()) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(price.trim());
        } catch (NumberFormatException e) {
            // 日志可选：System.err.println("Invalid price format: " + price);
            return BigDecimal.ZERO;
        }
    }



    //判断是否加拿大多伦多时间，且在冬/夏令时范围内
    public static boolean isWithinCATime(String timeStr) {
        DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        try {
            // 将时间字符串转换为 LocalDateTime
            LocalDateTime inputTime = LocalDateTime.parse(timeStr, FORMATTER);
            // 获取当前系统时间
            LocalDateTime currentTime = LocalDateTime.now();

            // 计算时间差，单位为秒
            long secondsDiff = Math.abs(ChronoUnit.SECONDS.between(inputTime, currentTime));

            // 判断时间差是否符合有效范围
            return secondsDiff > 60*60+120;
        } catch (Exception e) {
            e.printStackTrace();
            return true;
        }
    }


    /**
     * 下载远程文件并返回到浏览器
     *
     * @param fileUrl  文件的完整 URL 地址
     * @param fileName 下载时显示的文件名
     * @param response HttpServletResponse
     */
    public static void downloadFromUrl(String fileUrl, String fileName, HttpServletResponse response) {
        try (InputStream inputStream = new URL(fileUrl).openStream();
             OutputStream outputStream = response.getOutputStream()) {

            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" +
                    URLEncoder.encode(fileName, "UTF-8") + "\"");

            // 写入流数据
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }

            outputStream.flush();
        } catch (Exception e) {
            throw new RuntimeException("下载文件失败: " + e.getMessage(), e);
        }
    }

    //获取当天开始时间
    public static LocalDateTime getTodayStart() {
        return LocalDateTime.now().with(LocalTime.MIN);
    }

    //获取当天结束时间
    public static LocalDateTime getTodayEnd() {
        return LocalDateTime.now().with(LocalTime.MAX);
    }

    //获取本周开始时间
    public static LocalDateTime getThisWeekStart() {
        LocalDate currentDate = LocalDate.now();
        DayOfWeek firstDayOfWeek = DayOfWeek.MONDAY;
        LocalDate firstDayOfWeekDate = currentDate.with(TemporalAdjusters.previousOrSame(firstDayOfWeek));
        return firstDayOfWeekDate.atStartOfDay();
    }

    //获取本月开始时间
    public static LocalDateTime getThisMonthStart() {
        LocalDate currentDate = LocalDate.now();
        return currentDate.withDayOfMonth(1).atStartOfDay();
    }


    //计算两个时间之间的时间差（工作日除外）
    public static int TimeDifferenceChecker(LocalDateTime startTime, LocalDateTime endTime) {
        int totalHours = 0;
        LocalDateTime current = startTime;
        while (current.isBefore(endTime)) {
            if (current.getDayOfWeek() != DayOfWeek.SATURDAY && current.getDayOfWeek() != DayOfWeek.SUNDAY) {
                LocalDateTime nextDay = current.plusDays(1).truncatedTo(ChronoUnit.DAYS);
                if (nextDay.isAfter(endTime)) {
                    totalHours += ChronoUnit.HOURS.between(current, endTime);
                } else {
                    totalHours += ChronoUnit.HOURS.between(current, nextDay);
                }
            }
            current = current.plusDays(1).truncatedTo(ChronoUnit.DAYS);
        }
        return totalHours;
    }

    //获取昨天开始时间
    public static LocalDateTime getYesterdayStartTime() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        return LocalDateTime.of(yesterday, LocalTime.MIN);
    }

    //获取昨天结束时间
    public static LocalDateTime getYesterdayEndTime() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        return LocalDateTime.of(yesterday, LocalTime.MAX);
    }

    //获取指定格式当天时间
    public static Date getCurrentDateTime(String format) {
        // 定义时间格式
        SimpleDateFormat formatter = new SimpleDateFormat(format);

        // 获取当前时间字符串
        String formattedDate = formatter.format(new Date());

        // 解析回 Date 类型
        try {
            return formatter.parse(formattedDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }


    public static boolean isValidToken(Long tokenTime) {
        if (tokenTime == null) {
            return false;
        }
        long currentTimestamp = java.time.Instant.now().getEpochSecond();
        long diffInSeconds = Math.abs(currentTimestamp - tokenTime);
        return diffInSeconds < 25 * 24 * 60 * 60;

    }



    /**
     * 将 source 数组的内容拷贝到 target 数组的末尾
     * @param target 目标数组
     * @param source 源数组
     * @return 返回合并后的新数组
     */
    public static String[] copyArrayToEnd(String[] target, String[] source) {
        // 创建一个新的数组，大小为 target 数组和 source 数组的总和
        String[] result = new String[target.length + source.length];

        // 将 target 数组的内容拷贝到 result 数组
        System.arraycopy(target, 0, result, 0, target.length);

        // 将 source 数组的内容拷贝到 result 数组的后面
        System.arraycopy(source, 0, result, target.length, source.length);

        // 清除数组中的 null 元素
        List<String> nonNullList = new ArrayList<>();
        for (String element : result) {
            if (element != null) {
                nonNullList.add(element);
            }
        }
        // 将 List 转换为数组
        return nonNullList.toArray(new String[0]);
    }


    /**
     * 从 URL 读取 PDF，转成 Base64，再解码为 PdfReader
     */
    public static PdfReader createReaderFromUrl(String pdfUrl) throws Exception {
        LocalCacheMap cache = LocalCacheMap.getInstance();
        Object object = cache.get("pdf_base64");
        if (object != null) {
            byte[] pdfBytes = Base64.getDecoder().decode(object.toString());
            return new PdfReader(pdfBytes);
        }
        // 1. 读取 URL 中的 PDF 数据
        URL url = new URL(pdfUrl);
        try (InputStream is = url.openStream(); ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }

            // 2. 转 Base64
            String base64Pdf = Base64.getEncoder().encodeToString(baos.toByteArray());
            cache.set("pdf_base64", base64Pdf);

            // 3. 再解码为字节数组
            byte[] pdfBytes = Base64.getDecoder().decode(base64Pdf);

            // 4. 生成 PdfReader
            return new PdfReader(pdfBytes);
        }
    }


}
