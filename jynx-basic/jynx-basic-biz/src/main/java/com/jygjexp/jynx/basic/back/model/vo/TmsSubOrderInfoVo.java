package com.jygjexp.jynx.basic.back.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: xiongpengfei
 * @Description: 每个子单的信息（长cm、宽cm、高cm、重量kg、体积m³）询价入参
 * @Date: 2025/8/27 11:35
 */
@Data
@Schema(description = "每个子单的信息（长cm、宽cm、高cm、重量kg、体积m³）询价入参")
public class TmsSubOrderInfoVo {
    @Schema(description = "长cm")
    private BigDecimal length;

    @Schema(description = "宽cm")
    private BigDecimal width;

    @Schema(description = "高cm")
    private BigDecimal height;

    @Schema(description = "重量kg")
    private BigDecimal weight;

    @Schema(description = "体积m³")
    private BigDecimal volume;
}
