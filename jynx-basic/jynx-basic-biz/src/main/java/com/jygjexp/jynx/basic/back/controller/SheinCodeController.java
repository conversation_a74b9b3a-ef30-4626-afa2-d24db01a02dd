package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.admin.api.entity.SysDictItem;
import com.jygjexp.jynx.basic.back.api.feign.RemoteTmsService;
import com.jygjexp.jynx.basic.back.api.feign.RemoteUpmsService;
import com.jygjexp.jynx.basic.back.constants.OrderConstants;
import com.jygjexp.jynx.basic.back.entity.*;
import com.jygjexp.jynx.basic.back.model.bo.*;
import com.jygjexp.jynx.basic.back.model.vo.*;
import com.jygjexp.jynx.basic.back.model.vo.excel.OrderExcelVo;
import com.jygjexp.jynx.basic.back.model.vo.excel.SheinCodeExportVo;
import com.jygjexp.jynx.basic.back.model.vo.excel.SheinPickSignExcelVo;
import com.jygjexp.jynx.basic.back.service.*;
import com.jygjexp.jynx.basic.back.tools.EstimatedPriceBoValidator;
import com.jygjexp.jynx.common.core.constant.SecurityConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.sql.Wrapper;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 退件单
 *
 * <AUTHOR>
 * @date 2024-10-11 16:51:46
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/sheinCode")
@Tag(description = "sheinCode", name = "退件单管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SheinCodeController {

    private final OrderService OrderService;

    private final PackageParamService packageParamService;

    private final PostService postService;

    private final ApiAuthService apiAuthService;

    private final NbReturnExpenseRuleService nbReturnExpenseRuleService;

    private final RemoteTmsService remoteTmsService;


    /**
     * 分页查询
     *
     * @param requestVo 分页对象
     * @param
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping("/page")
    @PreAuthorize("@pms.hasPermission('back_SheinCode_view')")
    public R getSheinCodePage(@RequestBody SheinCodePageRequestVo requestVo) {
        return R.ok(OrderService.getPage(requestVo));
    }


    /**
     * 通过id查询退件单
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('back_SheinCode_view')")
    public R getById(@PathVariable("id") Integer id) {
        return R.ok(OrderService.getById(id));
    }


    /**
     * 查询需要发送短信的入站订单
     */
    @GetMapping("/sms")
    @Inner
    public List<ReturnSmsOrderVo> getSmsOrders() {
        return OrderService.getOrdersSendSms();
    }


    /**
     * 新增退件单
     *
     * @return R
     */
    @Operation(summary = "新增退件单", description = "新增退件单")
    @SysLog("新增退件单")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('back_SheinCode_add')")
    public R save(@RequestBody OrderInsertBo orderInsertBo) {
        return OrderService.saveOrder(orderInsertBo);
    }


    @Operation(summary = "返仓扫描", description = "返仓扫描")
    @SysLog("返仓扫描")
    @PostMapping("/returnScan")
    public R returnScan(@RequestParam String orderNo,String repeatOrder,Integer warehouseId,Boolean localFlag) {
        return OrderService.returnScan(orderNo,warehouseId,repeatOrder,localFlag);
    }

    @Operation(summary = "退回客户仓库", description = "退回客户仓库")
    @GetMapping("/returnCustomer")
    @PreAuthorize("@pms.hasPermission('back_SheinCode_edit')")
    public R returnCustomer(@ParameterObject SheinPageBo pageBo) {
        return OrderService.returnCustomer(pageBo);
    }


    @Operation(summary = "大件返仓扫描", description = "大件返仓扫描")
    @SysLog("大件返仓扫描")
    @PostMapping("/bigPackageReturnScan")
    public JSONObject bigPackageReturnScan(@RequestBody PackageParamBo packageParamBo) {
        return packageParamService.saveParam(packageParamBo);
    }



    @Operation(summary = "修改退件单", description = "修改退件单")
    @SysLog("修改退件单")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('back_SheinCode_edit')")
    public R updateById(@RequestBody SheinCodeEntity SheinCode) {
        return OrderService.updateReturnOrder(SheinCode);
    }

    /**
     * 通过id删除退件单
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除退件单", description = "通过id删除退件单")
    @SysLog("通过id删除退件单")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('back_SheinCode_del')")
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(OrderService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     *
     * @return excel 文件流
     */
    @ResponseExcel
    @PostMapping("/export")
    @PreAuthorize("@pms.hasPermission('back_SheinCode_export')")
    public List<OrderExcelVo> export(@RequestBody SheinCodeExportVo requestVo) {
         return OrderService.getExcel(requestVo);
    }

    /**
     * 导入订单 -- xls上传订单
     * @param file
     * @return
     */
    @Operation(summary = "导入退件单" , description = "导入退件单" )
    @SysLog("导入退件单" )
    @PostMapping("/_import")
    @PreAuthorize("@pms.hasPermission('back_SheinCode_import')")
    public R importOrders(@RequestParam("file") MultipartFile file) {
        return OrderService.processFile(file);
    }


    /*
    *  盲扫退件单状态已经退回客户仓库的订单，计算订单费用
    * */
    @Operation(summary = "查询订单状态为退回客户仓库",description = "查询订单状态为退回客户仓库")
    @Inner(value = false)
    @GetMapping("/selectRetuenOrderList")
    public List<SheinCodeEntity> selectRetuenOrderList(){

        // 获取前一天日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        // 前一天开始时间：00:00:00
        LocalDateTime startTime = LocalDateTime.of(yesterday, LocalTime.MIN);
        // 前一天结束时间：23:59:59
        LocalDateTime endTime = LocalDateTime.of(yesterday, LocalTime.MAX);
        // 格式化时间为字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String start = startTime.format(formatter);
        String end = endTime.format(formatter);

        //查询前一天已经退回客户仓库且没有计算金额的订单
        List<SheinCodeEntity> list = OrderService.list(new LambdaQueryWrapper<SheinCodeEntity>().eq(SheinCodeEntity::getIsDelete, 0)
                .eq(SheinCodeEntity::getStatus, OrderConstants.STATUS_10_RETURN_CUSTOMER)
                .and(wrapper -> wrapper.eq(SheinCodeEntity::getCalculateAmount, 0)
                        .or()
                        .isNull(SheinCodeEntity::getCalculateAmount))
                .between(SheinCodeEntity::getReturnCustomerTime,start,end));

        // 如果没有数据，直接返回空列表
        if (list.isEmpty()) {
            return Collections.emptyList();
        }


        List<SheinCodeEntity> sheinCodeEntityList = new ArrayList<>();
        for (SheinCodeEntity sheinCode : list) {
            PackageParamEntity packageOne = packageParamService.getOne(new LambdaQueryWrapper<PackageParamEntity>()
                    .eq(PackageParamEntity::getOrderNumber, sheinCode.getSheinCode()), false);
            if (null != packageOne && null != packageOne.getWeight()) {
                sheinCode.setWeight(packageOne.getWeight());
            }

            PostEntity postOne = postService.getOne(new LambdaQueryWrapper<PostEntity>().eq(PostEntity::getPostId, sheinCode.getPostId())
                    .eq(PostEntity::getIsValid, 1), false);
            if (null != postOne && null != postOne.getZip()){
                sheinCode.setZip(postOne.getZip());
            }

            sheinCodeEntityList.add(sheinCode);
        }

        return sheinCodeEntityList;
    }

    /*
     *  更新订单费用
     * */
    @Inner(value = false)
    @Operation(summary = "更新订单费用",description = "更新订单费用")
    @PostMapping("/updateOrdercost")
    public void updateOrdercost(@RequestBody SheinCodeEntity sheinCode){
        OrderService.updateById(sheinCode);
    }


    /*
     *  查询退件费用规则（根据合作商）
     * */
    @Inner(value = false)
    @Operation(summary = "查询退件费用规则（根据合作商）",description = "查询退件费用规则（根据合作商）")
    @GetMapping("/updateOrdercost/{authId}")
    public List<NbReturnExpenseRuleEntity> getReturnExpenseRule(@PathVariable("authId") Integer authId){
        ApiAuthEntity one = apiAuthService.getOne(new LambdaQueryWrapper<ApiAuthEntity>().eq(ApiAuthEntity::getId, authId).eq(ApiAuthEntity::getIsValild, 1));
        List<NbReturnExpenseRuleEntity> list = nbReturnExpenseRuleService.list(new LambdaQueryWrapper<NbReturnExpenseRuleEntity>()
                .eq(NbReturnExpenseRuleEntity::getPartnerName, one.getPartnerName())
                .eq(NbReturnExpenseRuleEntity::getStatus, 1));
        return list;
    }


    /*
     *  盲扫指定范围内退件单状态已经退回客户仓库的订单，计算订单费用
     * */
    @Operation(summary = "查询指定范围订单状态为退回客户仓库",description = "查询指定范围订单状态为退回客户仓库")
    @Inner(value = false)
    @GetMapping("/selectRetuenSectionOrderList")
    public List<SheinCodeEntity> selectRetuenSectionOrderList(){

        // 获取当前年份的1月1日
        LocalDate firstDayOfYear = LocalDate.of(LocalDate.now().getYear(), 1, 1);
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);

        // 设置查询的时间范围
        LocalDateTime startTime = LocalDateTime.of(firstDayOfYear, LocalTime.MIN); // 今年1月1日 00:00:00
        LocalDateTime endTime = LocalDateTime.of(yesterday, LocalTime.MAX); // 昨天 23:59:59
        // 格式化时间为字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String start = startTime.format(formatter);
        String end = endTime.format(formatter);

        //查询前一天已经退回客户仓库且没有计算金额的订单
        List<SheinCodeEntity> sheinCodeEntityList = OrderService.listRetuenSectionOrder();

        // 如果没有数据，直接返回空列表
        if (sheinCodeEntityList.isEmpty()) {
            return Collections.emptyList();
        }
        return sheinCodeEntityList;
    }


    /**
     * 统计监控寄出-签收的百分比
     *
     * @param requestVo 参数对象
     * @param
     * @return
     */
    @Operation(summary = " 统计监控寄出-签收的百分比", description = " 统计监控寄出-签收的百分比")
    @GetMapping("/listPickSign")
    @PreAuthorize("@pms.hasPermission('back_SheinCode_monitor')")
    public R listPickSign(@ParameterObject SheinPickSignBo requestVo) {
        return OrderService.listPickSign(requestVo);
    }


    /**
     * 导出监控寄出-签收的数据
     *
     * @param requestVo 参数对象
     * @param
     * @return
     */
    @ResponseExcel
    @Operation(summary = "导出监控寄出-签收的数据", description = "导出监控寄出-签收的数据")
    @GetMapping("/exportPickSign")
    @PreAuthorize("@pms.hasPermission('SheinCode_export_monitor')")
    public  List<SheinPickSignExcelVo>  exportPickSign(@ParameterObject SheinPickSignBo requestVo) {
        return OrderService.exportPickSign(requestVo);
    }



    @Operation(summary = "中大件询价", description = "中大件询价")
    @PostMapping("/order/estimatedPrice")
    public R getPrice(@RequestBody EstimatedPriceBo bo) {
        String message = EstimatedPriceBoValidator.validate(bo);
        if (StringUtils.isNotBlank(message)) {
            return R.failed(message);
        }
        TmsOrderPriceCalculationVo order = new TmsOrderPriceCalculationVo();
        BeanUtils.copyProperties(bo, order);
        order.setCustomerId(bo.getCustomerId()>10000?remoteTmsService.getTmsCustomerByUserId(bo.getCustomerId()).getId():bo.getCustomerId());
        PriceCalculationRequestVo para = new PriceCalculationRequestVo();
        para.setOrders(Collections.singletonList(order));
        List<TmsCargoInfoEntity> cargoInfoEntityList = bo.getCargoInfoEntityList();
        List<TmsSubOrderInfoVo> subOrderInfos = cargoInfoEntityList.stream()
                .map(cargo -> {
                    TmsSubOrderInfoVo vo = new TmsSubOrderInfoVo();
                    vo.setLength(cargo.getLength());
                    vo.setWidth(cargo.getWidth());
                    vo.setHeight(cargo.getHeight());
                    vo.setWeight(cargo.getWeight());
                    return vo;
                })
                .collect(Collectors.toList());
        order.setSubOrderInfos(subOrderInfos);
        order.setCargoType(bo.getCargoType());
        PriceCalculationResultVo priceCalculationResultVo = remoteTmsService.calculatePrice(para);
        PriceCalculationDetailVo priceCalculationDetailVo = priceCalculationResultVo.getDetails().get(0);
        if (priceCalculationResultVo.getSuccessCount()>0){
            PriceDetailVo priceDetailVo = new PriceDetailVo();
            priceDetailVo.setBasePrice(priceCalculationDetailVo.getBasePrice());
            priceDetailVo.setSurcharge(BigDecimal.ZERO);
            priceDetailVo.setFinalPrice(priceDetailVo.getBasePrice().add(priceDetailVo.getSurcharge()));
            return R.ok(priceDetailVo);
        }else {
            return R.failed(priceCalculationDetailVo.getErrorMessage());
        }
    }


}