package com.jygjexp.jynx.basic.back.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.basic.back.annotation.ConvertType;
import com.jygjexp.jynx.basic.back.tools.IntegerDictsConverter;
import com.jygjexp.jynx.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 退件单
 *
 * <AUTHOR>
 * @date 2024-10-11 21:36:17
 */
@Data
@TableName("tkzj_zt_shein_code")
@TenantTable
@EqualsAndHashCode(callSuper = true)
@Schema(description = "退件单")
public class SheinCodeEntity extends Model<SheinCodeEntity> {


    /**
     * id
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * sheinCode
     */
    @Schema(description = "sheinCode")
    private String sheinCode;

    /**
     * 生成日期
     */
    @Schema(description = "生成日期")
    private LocalDateTime createDate;

    /**
     * 驿站入站时间
     */
    @Schema(description = "驿站入站时间")
    private LocalDateTime postInTime;

    /**
     * 收件驿站
     */
    @Schema(description = "收件驿站")
    private Integer postId;

    /**
     * 状态：1=创建，2=驿站收件，3=司机收件，4=仓库收件
     */
    @Schema(description = "状态：1=创建，2=驿站收件，3=司机收件，4=仓库收件")
    private Integer status;

    /**
     * 司机
     */
    @Schema(description = "司机")
    private Integer driverId;

    /**
     * 司机取件时间
     */
    @Schema(description = "司机取件时间")
    private LocalDateTime driverTime;

    /**
     * 仓库ID
     */
    @Schema(description = "仓库ID")
    private Integer warehouseId;

    /**
     * 到达仓库时间
     */
    @Schema(description = "到达仓库时间")
    private LocalDateTime warehouseTime;

    /**
     * 驿站员工
     */
    @Schema(description = "驿站员工")
    private Integer postEmployeeId;

    /**
     * 客户单号
     */
    @Schema(description = "客户单号")
    private String customNo;

    /**
     * 国家
     */
    @Schema(description = "国家")
    private Integer countryId;

    /**
     * 地区
     */
    @Schema(description = "地区")
    private Integer provinceId;

    /**
     * 城市
     */
    @Schema(description = "城市")
    private Integer cityId;

    /**
     * 地址
     */
    @Schema(description = "地址")
    private String address;

    /**
     * 邮编
     */
    @Schema(description = "邮编")
    private String zip;

    /**
     * 收件人
     */
    @Schema(description = "收件人")
    private String consignee;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String mobile;

    /**
     * 重量
     */
    @Schema(description = "重量")
    private BigDecimal weight;

    /**
     * 价格
     */
    @Schema(description = "价格")
    private BigDecimal price;

    /**
     * 下单时间
     */
    @Schema(description = "下单时间")
    private LocalDateTime orderTime;

    /**
     * 邮编
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 删除
     */
    @Schema(description = "删除")
    private Integer isDelete;

    /**
     * 删除时间
     */
    @Schema(description = "删除时间")
    private LocalDateTime deleteTime;

    /**
     * 合作方账号
     */
    @Schema(description = "合作方账号")
    private Integer authId;

    /**
     * 账单id
     */
    @Schema(description = "账单id")
    private Integer billId;

    /**
     * 结算金额
     */
    @Schema(description = "结算金额")
    private BigDecimal amount;

    /**
     * 省名
     */
    @Schema(description = "省名")
    private String provinceName;

    /**
     * 市名
     */
    @Schema(description = "市名")
    private String cityName;

    /**
     * 国名
     */
    @Schema(description = "国名")
    private String countryName;

    /**
     * 第三方单号
     */
    @Schema(description = "第三方单号")
    private String thirdLogiNo;

    /**
     * 发件国家
     */
    @Schema(description = "发件国家")
    private String senderCountryName;

    /**
     * 发件省
     */
    @Schema(description = "发件省")
    private String senderProvinceName;

    /**
     * 发件城市
     */
    @Schema(description = "发件城市")
    private String senderCityName;

    /**
     * 发件地址
     */
    @Schema(description = "发件地址")
    private String senderAddress;

    /**
     * 发件人邮编
     */
    @Schema(description = "发件人邮编")
    private String senderPostalcode;

    /**
     * 发件人
     */
    @Schema(description = "发件人")
    private String senderName;

    /**
     * 发件手机
     */
    @Schema(description = "发件手机")
    private String senderMobile;

    /**
     * 包裹重量
     */
    @Schema(description = "包裹重量")
    private BigDecimal packageWeight;

    /**
     * 包裹长
     */
    @Schema(description = "包裹长")
    private BigDecimal packageLength;

    /**
     * 包裹宽
     */
    @Schema(description = "包裹宽")
    private BigDecimal packageWidth;

    /**
     * 包裹高
     */
    @Schema(description = "包裹高")
    private BigDecimal packageHeight;

    /**
     * ups id
     */
    @Schema(description = "ups id")
    private String upsId;

    /**
     * ups waybill no.
     */
    @Schema(description = "ups waybill no.")
    private String upsWaybillNumber;

    /**
     * ups price
     */
    @Schema(description = "ups price")
    private BigDecimal upsPrice;

    /**
     * ups price symbol
     */
    @Schema(description = "ups price symbol")
    private String upsPriceSymbol;

    /**
     * 面单加密串
     */
    @Schema(description = "面单加密串")
    private String labelCode;

    /**
     * 面单路径
     */
    @Schema(description = "面单路径")
    private String labelPath;

    /**
     * ups订单
     */
    @Schema(description = "ups订单")
    private Integer isUps;

    /**
     * label下载成功
     */
    @Schema(description = "label下载成功")
    private Integer labelOk;

    /**
     * 导入管理员
     */
    @Schema(description = "导入管理员")
    private Integer adminId;

    /**
     * 收款账单ID
     */
    @Schema(description = "收款账单ID")
    private Integer receiptId;

    /**
     * 收款金额
     */
    @Schema(description = "收款金额")
    private BigDecimal receiptAmount;

    /**
     * 收款特殊时段
     */
    @Schema(description = "收款特殊时段")
    private Integer isReceiptSpecial;

    /**
     * 发件人地址2
     */
    @Schema(description = "发件人地址2")
    private String senderAddress2;

    /**
     * 发件人地址3
     */
    @Schema(description = "发件人地址3")
    private String senderAddress3;

    /**
     * 收件人地址2
     */
    @Schema(description = "收件人地址2")
    private String receiveAddress2;

    /**
     * 收件人地址3
     */
    @Schema(description = "收件人地址3")
    private String receiveAddress3;

    /**
     * 订单备注
     */
    @Schema(description = "订单备注")
    private String note;

    /**
     * 挂起状态，不纳入统计
     */
    @Schema(description = "挂起状态，不纳入统计")
    private Integer hangUp;

    /**
     * 子渠道代码
     */
    @Schema(description = "子渠道代码")
    @ExcelProperty(value = "(Subchannel code)子渠道代码", converter = IntegerDictsConverter.class)
    @ConvertType("subChannel")
    private String subChannel;

    /**
     * 易仓面单path
     */
    @Schema(description = "易仓面单path")
    private String ycLabelPath;

    /**
     * 易仓渠道
     */
    @Schema(description = "易仓渠道")
    private Integer isYicang;

    /**
     * 退回客户时间
     */
    @Schema(description = "退回客户时间")
    private LocalDateTime returnCustomerTime;

    /**
     * 手机号
     */
    private String shipperPhone;

    /**
     * 退件回执
     */
    private String receiptImg;

    /**
     * 批次号
     */
    private Long batchNo;

    /**
     * 是否已经推送
     */
    private Boolean pushFlag ;


    /**
     * 计费价格
     */
    private BigDecimal calculateAmount;


    /**
     * 面单状态
     */
    private Integer damaged;


    /**
     * 相关图片
     */
    private String damagedUrl;

    /**
     * 是否驿站打印
     */
    private Boolean printFlag;

    /**
     * 打印驿站
     */
    private Integer printPost;


    /**
     * 打印时间
     */
    private LocalDateTime printTime;


    /**
     * 租户号
     */
    @Schema(description = "租户号")
    private Long tenantId;


    /**
     * 来源城市
     */
    @Schema(description = "来源城市")
    private String sourceCity;


    /**
     * png面单
     */
    @Schema(description = "png面单")
    private String labelPng;


    /**
     * 订单费用计算备注
     */
    @Schema(description = "订单费用计算备注")
    private String orderCostRemark;


    /**
     * 非合作驿站信息备注
     */
    @Schema(description = "非合作驿站信息备注")
    private String postInfo;

    /**
     * 拦截时间
     */
    @Schema(description = "拦截时间")
    private LocalDateTime blockTime;

    /**
     * 拦截时间
     */
    @Schema(description = "拦截状态")
    private Boolean blockStatus;

    /**
     * 一票多件
     */
    @Schema(description = "一票多件")
    private Boolean many;


    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String customerName;


    /**
     * 发件人国省市（统一版）
     */
    @Schema(description = "发件人国省市（统一版）")
    private String senderUnifiedAddress;


    /**
     * 收件人国省市（统一版）
     */
    @Schema(description = "收件人国省市（统一版）")
    private String consigneeUnifiedAddress;


}