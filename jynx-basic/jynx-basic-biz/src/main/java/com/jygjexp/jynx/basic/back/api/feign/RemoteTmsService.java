package com.jygjexp.jynx.basic.back.api.feign;


import com.jygjexp.jynx.basic.back.dto.PriceCalculationDTO;
import com.jygjexp.jynx.basic.back.dto.TmsSortingTemplateDto;
import com.jygjexp.jynx.basic.back.entity.*;
import com.jygjexp.jynx.basic.back.model.bo.ApiOrder;
import com.jygjexp.jynx.basic.back.model.vo.*;
import com.jygjexp.jynx.common.core.constant.ServiceNameConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.feign.annotation.NoToken;
import com.jygjexp.jynx.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@FeignClient(contextId = "RemoteTmsService", value = ServiceNameConstants.TMS_SERVICE)
public interface RemoteTmsService {

    /**
     * 获取卡派客户信息
     *
     * @return 用户信息
     */
    @GetMapping("/tmsCustomer/getByCode")
    TmsCustomerEntity getTmsCustomerByCode(@RequestParam("code") String code);

    /**
     * 获取卡派客户信息
     *
     * @return 用户信息
     */
    @GetMapping("/tmsCustomer/getByToken")
    TmsCustomerEntity getTmsCustomerByToken(@RequestParam("token") String token);




    /**
     * 获取客户信息
     *
     * @return 用户信息
     */
    @GetMapping("/tmsCustomer/getByUserId")
    TmsCustomerEntity getTmsCustomerByUserId(@RequestParam("userId") Long userId);



    /**
     * 获取卡派客户订单信息
     */
    @GetMapping("/tms/label/getCustomerOrder")
    TmsCustomerOrderEntity getCustomerOrder(@RequestParam("trackingNo") String trackingNo,
                                            @RequestParam(value = "isReferenceNumber", required = false) Boolean isReferenceNumber);


    /**
     * 根据子单号获取订单信息(主要用于面单)
     *
     * @param trackingNo
     * @return
     */
    @GetMapping("/tms/label/getCustomerOrderBySubOrder")
    TmsCustomerOrderEntity getCustomerOrderBySubOrder(@RequestParam("trackingNo") String trackingNo);


    /**
     * 获取订单POD信息
     * @param trackingNo
     * @return
     */
    @Operation(summary = "获取订单POD信息", description = "获取订单POD信息")
    @GetMapping("/tms/label/getPod")
    PodVo getCustomerOrderPod(@RequestParam("trackingNo")String trackingNo);


    /**
     * 获取订单POD信息
     * @param trackingNo
     * @return
     */
    @Operation(summary = "获取子订单POD信息", description = "获取子订单POD信息")
    @GetMapping("/tms/label/getSubOrderPod")
    List<SubOrderPodVo> getSubOrderPod(@RequestParam("trackingNo")String trackingNo);

    /**
     * 根据客户id，查询是否推送UNI(主要用于面单)
     *
     * @param customerId
     * @return
     */
    @GetMapping("/tmsCustomer/selectIsPush/{customerId}")
    @NoToken
    String selectIsPush(@RequestParam("customerId") Long customerId);

    /**
     * 获取中大件容器标签信息
     */
    @GetMapping("/tmsLabel/getByLabelCode")
    List<TmsLabelEntity> getByLabelCode(@RequestParam("labelCode") String labelCode);

    /**
     * 获取中大件格口编码信息
     */
    @GetMapping("/tmsSortingGrid/getByGridIdNos")
    List<TmsSortingGridEntity> getByGridIdNos(@RequestParam("gridIds") Long[] gridIds);

    /**
     * 获取仓库名称
     */
    @GetMapping("/tmsSite/getWarehouseName")
    String getWarehouseName(@RequestParam Long warehouseId);

    /**
     * 下单到卡派
     */
    @PostMapping("/apiOrder/createApiOrder")
    R pushOrder(@RequestBody ApiOrder apiOrder);


    /**
     * 获取中大件全部有效邮编
     */
    @PostMapping("/apiOrder/getZipCode")
    String getZipCode();


    /**
     * 卡派删除订单接口
     */
    @PostMapping("/apiOrder/delete")
    R deleteOrder(@RequestParam("orderNo") String orderNo);


    /**
     * 卡派拦截订单接口
     */
    @PostMapping("/apiOrder/block")
    R blockOrder(@RequestParam("orderNo") String orderNo);


    /**
     * 卡派获取轨迹接口
     */
    @PostMapping("/apiOrder/track")
    R track(@RequestParam("orderNo") String orderNo);


    /**
     * 获取路线接口
     */
    @GetMapping("/tmsRegion/getRouteNo")
    @ResponseBody
    String getRouteNo(@RequestParam("city") String city, @RequestParam("zipCode") String zipCode);


    /**
     * 换单接口
     */
    @PostMapping("/thirdPartPost/exchangeOrder")
    R exchangeOrder(@RequestBody ExchangeVo vo);


    /**
     * 获取UPS token
     */
    @PostMapping("/apiOrder/getUpsToken")
    String getUpsToken();


    /**
     * 根据id查询上架自提记录--打印取件码远程使用
     */
    @Operation(summary = "根据id查询上架自提记录--打印取件码远程使用" , description = "根据id查询上架自提记录--打印取件码远程使用" )
    @PostMapping("/tmsZdjPickup/getPickUpCodeByIdForRemote")
    List<TmsZdjPickupEntity> getPickUpCodeByIdForRomote(@RequestBody Map<String, List<Long>> ids);

    /**
     * 获取此时时启用的唯一的分拣模版
     */
    @Operation(summary = "获取此时时启用的唯一的分拣模版" , description = "获取此时时启用的唯一的分拣模版" )
    @GetMapping("/tmsSortingTemplate/getEnableSortingTemplate")
    TmsSortingTemplateDto getEnableSortingTemplate();


    /**
     * 根据单号获取订单信息
     *
     * @param orderNo
     * @return
     */
    @GetMapping("/tmsCustomerOrder/getCustomerOrderByOrderNo")
    TmsCustomerOrderEntity getCustomerOrderByOrderNo(@RequestParam("orderNo") String orderNo);

    /**
     * 新增分拣记录
     * @param tmsSortingRecord 分拣记录
     * @return R
     */
    @Operation(summary = "新增分拣记录" , description = "新增分拣记录" )
    @PostMapping("/tmsSortingRecord/add")
    R save(@RequestBody TmsSortingRecordEntity tmsSortingRecord);

    /**
     * 分拣机分拣后将订单状态改为已入库（加了一个字段）-并保存轨迹
     */
    @Operation(summary = "根据跟踪单号查询订单" , description = "根据跟踪单号查询订单" )
    @PostMapping("/tmsCustomerOrder/sortingUpdate" )
    R sortingUpdate(@RequestParam("orderNo") String orderNo);

    /**
     * 分拣机分拣 -并保存轨迹 & 订单状态变更
     */
    @NoToken
    @Operation(summary = "根据跟踪单号查询订单" , description = "根据跟踪单号查询订单" )
    @PostMapping("/tmsCustomerOrder/sortingOperated" )
    R sortingOperated(@RequestParam("entrustedOrderNumbers") List<String> entrustedOrderNumbers);


    /**
     * 17track查询Nb中大件轨迹
     *
     */
    @Operation(summary = "17track查询Nb中大件轨迹", description = "17track查询Nb中大件轨迹")
    @PostMapping("/apiOrder/sq/track")
    @Inner(value = false)
    R sqTrack(@RequestBody TmsTrackRequest request);


    /**
     * 小包批量订单轨迹查询（代替原zxoms接口）
     * @return 批量轨迹查询结果
     */
    @Operation(summary = "小包批量订单轨迹查询", description = "小包批量订单轨迹查询 - 迁移自zxoms")
    @PostMapping("/apiOrder/tracks")
    @NoToken
    R getZxomsTracks(@RequestBody TrackQueryVo request) ;

    /**
     * 获取UPS轨迹信息
     */
    @GetMapping("/tms/label/getTrack")
    String getTrack(@RequestParam("inquiryNumber") String inquiryNumber);


    /**
     * API询价接口
     */
    @PostMapping("/apiOrder/calculatePrice")
    PriceCalculationResultVo calculatePrice(@RequestBody PriceCalculationRequestVo request);


    @PostMapping("/apiOrder/pay")
    void pay(@RequestParam("amount") BigDecimal amount,
             @RequestParam("payType") String payType,
             @RequestParam("userId") Long userId);

    /**
     * 包裹入仓核算订单价格
     */
    @Operation(summary = "包裹入仓核算订单价格", description = "包裹入仓核算订单价格")
    @PostMapping("/tmsReceivable/calculate-price-and-create")
    @NoToken
    @Inner(value = false)
    String calculatePrice(@RequestBody PriceCalculationDTO priceCalculationDTO);


    /**
     * 新增用户余额
     * @param payParaVo 参数对象
     * @return true 添加成功，false 失败
     */
    @PostMapping("tms/pay/addBalance")
    Boolean addBalance(@RequestBody PayParaVo payParaVo);

    /**
     * 订单新增复核体积和重量
     */
    @Operation(summary = "订单新增复核体积和重量", description = "订单新增复核体积和重量")
    @PostMapping("/tmsCustomerOrder/addReviewVolumeAndWeight")
    @NoToken
    String addReviewVolumeAndWeight(@RequestParam("volume") BigDecimal volume, @RequestParam("weight") BigDecimal weight, @RequestParam String orderNo);

    /**
     * 保存订单批次信息
     */
    @Operation(summary = "保存订单批次信息", description = "保存订单批次信息")
    @PostMapping("/tmsCustomerOrder/saveBatchOrder")
    @NoToken
    Boolean saveBatchOrder(@RequestParam("orderNo") String orderNo);


}
