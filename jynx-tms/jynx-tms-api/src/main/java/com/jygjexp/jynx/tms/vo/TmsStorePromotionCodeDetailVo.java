package com.jygjexp.jynx.tms.vo;

import com.jygjexp.jynx.tms.dto.TmsStorePromotionCodeDiscountDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: daiyuxuan
 * @create: 2025/8/13
 */

@Data
public class TmsStorePromotionCodeDetailVo {
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 推广人ID
     */
    @Schema(description = "推广人ID")
    private Long promoterId;

    /**
     * 优惠码
     */
    @Schema(description = "优惠码")
    private String code;

    /**
     * 服务商适用ID
     */
    @Schema(description = "服务商适用ID")
    private List<Long> providerIds;

//    /**
//     * 使用次数
//     */
//    @Schema(description = "使用次数")
//    private Integer useLimit;


    /**
     * 是否可重复使用
     */
    @Schema(description = "是否可重复使用 0:不可重复使用;1:可重复使用")
    private Integer repeatable;


    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间")
    private LocalDate validStartTime;

    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间")
    private LocalDate validEndTime;

    /**
     * 佣金类型：0、固定金额；1、固定比例
     */
    @Schema(description = "佣金类型：0、固定金额；1、固定比例")
    private Integer commissionType;

    /**
     * 佣金值（固定金额或比例）
     */
    @Schema(description = "佣金值（固定金额或比例）")
    private BigDecimal commissionValue;

    /**
     * 是否首次返回佣金：0、固定金额；1、比例
     */
    @Schema(description = "是否首次返回佣金：0、固定金额；1、比例")
    private Integer firstUseCommissionType;

    /**
     * 折扣类型：0、满减；1、打折
     */
    @Schema(description = "折扣类型：0、满减；1、打折")
    private Integer discountType;

    /**
     * 折扣信息
     */
    @Schema(description = "折扣信息")
    private List<TmsStorePromotionCodeDiscountDTO> discounts;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 有效状态  0可用 1不可用
     */
    @Schema(description = "有效状态")
    private Integer validStatus;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;
}

