package com.jygjexp.jynx.tms.vo.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: xiongpengfei
 * @Description: 中大件App-派送司机（子单纬度）配送成功参数
 * @Date: 2025/6/30 17:22
 */
@Data
@FieldNameConstants
@Schema(description = "中大件App-派送司机（子单纬度）配送成功参数")
public class TmsAppDriverSubOrderDeliveryVo {

    @Schema(description="跟踪单号")
    @NotNull
    private List<String> subOrderList;

    @Schema(description="送货证明")
    @NotBlank
    private String deliveryProof;

    @Schema(description="电子签名URL")
    private String esignatureUrl;

    @Schema(description="揽收、派送标识")
    private Integer isTask;

}
