package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * API询价明细
 */
@Data
public class PriceDetailVo {

    /**
     * 基础价格
     */
    @Schema(description = "基础价格(CAD)")
    private BigDecimal basePrice;

    /**
     * 附加费
     */
    @Schema(description = "附加费(CAD)")
    private BigDecimal surcharge;

    /**
     * 税费
     */
    @Schema(description="税费")
    private BigDecimal taxFee;

    /**
     * 税率
     */
    @Schema(description = "税率")
    private BigDecimal profitRate;


    /**
     * 最终计算价格（基础价格 + 利润率）
     */
    @Schema(description = "最终计算价格(CAD)")
    private BigDecimal finalPrice;


    /**
     * 费用详情
     */
    @Schema(description = "附加费详情")
    List<TmsFeeRuleDetailVo> feeRules;




}
