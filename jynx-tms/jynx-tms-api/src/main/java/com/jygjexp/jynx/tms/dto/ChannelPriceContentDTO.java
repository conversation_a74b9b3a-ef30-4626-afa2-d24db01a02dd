package com.jygjexp.jynx.tms.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
public class ChannelPriceContentDTO {

    @Schema(description = "总长度")
    private BigDecimal totalLength;

    @Schema(description = "总宽度")
    private BigDecimal totalWidth;

    @Schema(description = "总高度")
    private BigDecimal totalHeight;

    @Schema(description = "总重量")
    private BigDecimal totalWeight;

    @Schema(description = "总体积(m³)")
    private BigDecimal totalVolume;

    @Schema(description = "总体积(cm³)")
    private BigDecimal totalVolumeCm3;

    @Schema(description = "收货地邮编")
    private String receiverPostalCode;

    @Schema(description = "发货地邮编")
    private String shipperPostalCode;

}
