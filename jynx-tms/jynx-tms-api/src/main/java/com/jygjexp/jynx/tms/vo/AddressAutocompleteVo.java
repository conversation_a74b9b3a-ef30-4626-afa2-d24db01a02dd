package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 地址自动补全响应VO
 *
 * <AUTHOR>
 * @date 2025-09-02
 */
@Data
@Schema(description = "地址自动补全响应")
public class AddressAutocompleteVo {

    /**
     * ID
     */
    @Schema(description = "ID")
    private Integer id;

    /**
     * 三字邮编
     */
    @Schema(description = "三字邮编")
    private String threePostCode;

    /**
     * 六字邮编
     */
    @Schema(description = "六字邮编")
    private String sixPostCode;

    /**
     * 城市
     */
    @Schema(description = "城市")
    private String city;

    /**
     * 省州
     */
    @Schema(description = "省州")
    private String province;

    /**
     * 街道
     */
    @Schema(description = "街道")
    private String street;

    /**
     * 完整地址（格式化显示）
     */
    @Schema(description = "完整地址")
    private String fullAddress;

    /**
     * 匹配类型（用于前端高亮显示）
     */
    @Schema(description = "匹配类型：POSTCODE-邮编匹配，CITY-城市匹配，STREET-街道匹配")
    private String matchType;
}
