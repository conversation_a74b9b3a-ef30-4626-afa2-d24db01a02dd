package com.jygjexp.jynx.tms.dto;

import com.jygjexp.jynx.tms.entity.TmsServiceQuoteEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

/**
 * 服务商报价分页返回参数
 *
 * <AUTHOR>
 * @date 2025-07-10 10:44:07
 */
@Data
@Schema(description = "服务商报价分页返回查询参数")
public class TmsServiceQuoteDto extends TmsServiceQuoteEntity {

    /**
     * 服务商名称
     */
    @Schema(description="服务商名称")
    private String providerName;

    /**
     * 可达分区
     */
    @Schema(description="可达分区")
    private String reachableRegionName;

    /**
     * 不可达分区
     */
    @Schema(description="不可达分区")
    private String unreachableRegionName;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    private String channelName;


}