package com.jygjexp.jynx.tms.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: daiyuxuan
 * @create: 2025/8/29
 */
@Data
public class StoreOrderEmailTemplateDTO {
    @Schema(description = "跟踪单号")
    private String entrustedOrderNumber;

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "送货地址")
    private String deliveryAddress;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "门店地址")
    private String storeAddress;

    @Schema(description = "发送邮件")
    private String sendEmail;
}
