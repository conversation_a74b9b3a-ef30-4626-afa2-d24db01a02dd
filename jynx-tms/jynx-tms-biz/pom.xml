<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
            http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jygjexp</groupId>
        <artifactId>jynx-tms</artifactId>
        <version>5.6.0</version>
    </parent>

    <artifactId>jynx-tms-biz</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.sparkjava</groupId>
            <artifactId>spark-core</artifactId>
            <version>2.9.3</version>
        </dependency>
        <dependency>
            <groupId>com.stripe</groupId>
            <artifactId>stripe-java</artifactId>
            <version>29.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.jygjexp</groupId>
            <artifactId>jynx-common-idempotent</artifactId>
        </dependency>
        <!--lock4j -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>lock4j-redis-template-spring-boot-starter</artifactId>
            <version>2.2.5</version>
        </dependency>
        <!--PDF合并工具-->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.27</version>
        </dependency>
        <!--Elasticsearch-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>

        <!--必备: undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <!--必备: spring boot web-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--必备: 注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--必备: 配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--必备: 操作数据源相关-->
        <dependency>
            <groupId>com.jygjexp</groupId>
            <artifactId>jynx-common-data</artifactId>
        </dependency>
        <!--必备：pigx安全模块-->
        <dependency>
            <groupId>com.jygjexp</groupId>
            <artifactId>jynx-common-security</artifactId>
        </dependency>
        <!--必备：xss 过滤模块-->
        <dependency>
            <groupId>com.jygjexp</groupId>
            <artifactId>jynx-common-xss</artifactId>
        </dependency>
        <!--必备: sentinel 依赖-->
        <dependency>
            <groupId>com.jygjexp</groupId>
            <artifactId>jynx-common-sentinel</artifactId>
        </dependency>
        <!--必备: feign 依赖-->
        <dependency>
            <groupId>com.jygjexp</groupId>
            <artifactId>jynx-common-feign</artifactId>
        </dependency>
        <!--必备: 依赖api模块-->
        <dependency>
            <groupId>com.jygjexp</groupId>
            <artifactId>jynx-tms-api</artifactId>
            <version>5.6.0</version>
        </dependency>
        <!--必备: log 依赖-->
        <dependency>
            <groupId>com.jygjexp</groupId>
            <artifactId>jynx-common-log</artifactId>
        </dependency>
        <!--选配: mybatis 依赖 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <!--选配： druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <!--选配: mysql 数据库驱动 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!--选配: swagger文档-->
        <dependency>
            <groupId>com.jygjexp</groupId>
            <artifactId>jynx-common-swagger</artifactId>
        </dependency>
        <!--测试: spring boot test-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <!--	WebSocket	-->
        <dependency>
            <groupId>com.jygjexp</groupId>
            <artifactId>jynx-common-websocket</artifactId>
        </dependency>

        <!--	xxl-job 定时作业调度	-->
        <dependency>
            <groupId>com.jygjexp</groupId>
            <artifactId>jynx-common-job</artifactId>
            <version>5.6.0</version>
        </dependency>

        <!--必备: 依赖api模块-->
        <dependency>
            <groupId>com.jygjexp</groupId>
            <artifactId>jynx-zxoms-api</artifactId>
            <version>5.6.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.16</version>
            <scope>compile</scope>
        </dependency>

        <!-- 阿里云oss依赖 -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.17.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.10.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <!--RibbitMQ依赖 -->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-amqp</artifactId>-->
<!--        </dependency>-->

    </dependencies>
    <profiles>
        <profile>
            <id>cloud</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>io.fabric8</groupId>
                        <artifactId>docker-maven-plugin</artifactId>
                        <configuration>
                            <skip>false</skip>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>boot</id>
        </profile>
    </profiles>
</project>
