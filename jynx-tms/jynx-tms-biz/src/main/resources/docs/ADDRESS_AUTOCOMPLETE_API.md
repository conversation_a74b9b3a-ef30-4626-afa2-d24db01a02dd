# 地址自动补全API使用指南

## 概述

基于TmsBaseCityStreetPostcodeEntity地址库实体类实现的地址自动补全API接口，支持加拿大全国700多万条邮编数据的高性能搜索。

## 功能特性

- ✅ 支持邮编(postcode)、城市(city)、街道(street)关键词搜索
- ✅ 支持模糊匹配、前缀匹配和分词匹配
- ✅ 双重搜索引擎支持：Elasticsearch + MySQL降级
- ✅ 毫秒级查询响应
- ✅ 智能排序和去重逻辑
- ✅ 完整的地址信息返回

## API接口

### 1. 地址自动补全

**接口地址：** `GET /tmsBaseCityStreetPostcode/autocomplete`

**请求参数：**
- `keyword` (必需): 搜索关键词，支持邮编/城市/街道
- `limit` (必需): 返回结果的最大条数，范围1-50

**请求示例：**
```bash
# 搜索邮编
GET /tmsBaseCityStreetPostcode/autocomplete?keyword=M5V&limit=10

# 搜索城市
GET /tmsBaseCityStreetPostcode/autocomplete?keyword=Toronto&limit=15

# 搜索街道
GET /tmsBaseCityStreetPostcode/autocomplete?keyword=King Street&limit=20
```

**响应格式：**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": [
    {
      "id": 12345,
      "threePostCode": "M5V",
      "sixPostCode": "M5V1A1",
      "city": "Toronto",
      "province": "Ontario",
      "street": "King Street West",
      "fullAddress": "King Street West, Toronto, Ontario M5V1A1",
      "matchType": "MIXED"
    }
  ]
}
```

### 2. 数据同步到Elasticsearch

**接口地址：** `POST /tmsBaseCityStreetPostcode/sync-to-elasticsearch`

**功能：** 将数据库中的地址数据同步到Elasticsearch索引

**响应示例：**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": "数据同步成功"
}
```

## 部署配置

### 方案一：仅使用MySQL（推荐用于快速部署）

1. **执行数据库索引优化：**
```sql
-- 执行 sql/address_index_optimization.sql 中的索引创建语句
```

2. **配置文件设置：**
```yaml
address:
  search:
    elasticsearch:
      enabled: false  # 禁用Elasticsearch，使用MySQL
```

### 方案二：使用Elasticsearch（推荐用于生产环境）

1. **安装Elasticsearch 7.x+：**
```bash
# Docker方式安装
docker run -d --name elasticsearch \
  -p 9200:9200 -p 9300:9300 \
  -e "discovery.type=single-node" \
  -e "ES_JAVA_OPTS=-Xms512m -Xmx512m" \
  elasticsearch:7.17.0
```

2. **配置文件设置：**
```yaml
spring:
  elasticsearch:
    uris: http://localhost:9200
    connection-timeout: 10s
    socket-timeout: 30s

address:
  search:
    elasticsearch:
      enabled: true  # 启用Elasticsearch
      batch-size: 1000
      max-result-size: 50
```

3. **数据同步：**
```bash
# 调用同步接口
POST /tmsBaseCityStreetPostcode/sync-to-elasticsearch
```

## 性能优化建议

### MySQL优化
1. 确保已创建必要的索引
2. 定期执行 `ANALYZE TABLE` 更新统计信息
3. 监控慢查询日志

### Elasticsearch优化
1. 根据数据量调整分片数量
2. 配置合适的JVM堆内存
3. 使用SSD存储提升I/O性能

## 监控和维护

### 性能监控
- 查询响应时间
- 搜索准确率
- 系统资源使用情况

### 数据维护
- 定期同步增量数据
- 监控索引大小和性能
- 备份重要配置和数据

## 故障排除

### 常见问题

1. **Elasticsearch连接失败**
   - 检查ES服务状态
   - 验证网络连接
   - 确认认证信息

2. **搜索结果不准确**
   - 检查数据同步状态
   - 验证索引映射配置
   - 调整搜索权重

3. **查询性能慢**
   - 检查数据库索引
   - 优化查询条件
   - 考虑启用Elasticsearch

## 技术架构

```
用户请求 → Controller → Service → 搜索引擎选择
                                    ├── Elasticsearch (优先)
                                    └── MySQL (降级)
```

## 联系支持

如有问题请联系开发团队或查看相关文档。
