-- 地址库表索引优化SQL
-- 用于提升地址自动补全查询性能

-- 1. 为三字邮编创建前缀索引（用于前缀匹配）
CREATE INDEX idx_three_post_code_prefix ON tms_base_city_street_postcode (three_post_code(6));

-- 2. 为六字邮编创建前缀索引（用于前缀匹配）
CREATE INDEX idx_six_post_code_prefix ON tms_base_city_street_postcode (six_post_code(8));

-- 3. 为城市名称创建索引（用于模糊匹配）
CREATE INDEX idx_city ON tms_base_city_street_postcode (city);

-- 4. 为街道名称创建索引（用于模糊匹配）
CREATE INDEX idx_street ON tms_base_city_street_postcode (rstreet);

-- 5. 为状态字段创建索引（用于过滤有效数据）
CREATE INDEX idx_status ON tms_base_city_street_postcode (status);

-- 6. 创建复合索引：状态+三字邮编（最常用的查询组合）
CREATE INDEX idx_status_three_post_code ON tms_base_city_street_postcode (status, three_post_code);

-- 7. 创建复合索引：状态+城市（城市搜索优化）
CREATE INDEX idx_status_city ON tms_base_city_street_postcode (status, city);

-- 8. 创建全文索引（MySQL 5.7+支持，用于更好的文本搜索）
-- 注意：这个索引可能会占用较多存储空间，根据实际需要决定是否创建
-- ALTER TABLE tms_base_city_street_postcode ADD FULLTEXT(city, rstreet);

-- 查看表的索引信息
-- SHOW INDEX FROM tms_base_city_street_postcode;

-- 分析表统计信息（建议在创建索引后执行）
-- ANALYZE TABLE tms_base_city_street_postcode;

-- 性能测试查询示例：
-- 1. 三字邮编前缀查询
-- SELECT * FROM tms_base_city_street_postcode 
-- WHERE status = 1 AND three_post_code LIKE 'M5V%' 
-- ORDER BY three_post_code LIMIT 10;

-- 2. 城市模糊查询
-- SELECT * FROM tms_base_city_street_postcode 
-- WHERE status = 1 AND city LIKE '%Toronto%' 
-- ORDER BY city LIMIT 10;

-- 3. 多字段组合查询
-- SELECT * FROM tms_base_city_street_postcode 
-- WHERE status = 1 AND (
--   three_post_code LIKE 'M5V%' OR 
--   six_post_code LIKE 'M5V1A1%' OR 
--   city LIKE '%Toronto%' OR 
--   rstreet LIKE '%King%'
-- ) 
-- ORDER BY three_post_code, city, rstreet LIMIT 10;
