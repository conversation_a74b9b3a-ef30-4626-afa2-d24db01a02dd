# Elasticsearch配置示例
# 将此配置添加到主配置文件中或通过Nacos配置中心管理

spring:
  elasticsearch:
    # Elasticsearch服务器地址
    uris: http://localhost:9200
    # 连接超时时间
    connection-timeout: 10s
    # Socket超时时间
    socket-timeout: 30s
    # 用户名
    username: 
    # 密码
    password: 

# 地址搜索相关配置
address:
  search:
    elasticsearch:
      # 是否启用Elasticsearch搜索（默认false，使用MySQL）
      enabled: false
      # 索引名称
      index-name: address_autocomplete
      # 批量同步数据的批次大小
      batch-size: 1000
      # 搜索结果最大数量限制
      max-result-size: 50

