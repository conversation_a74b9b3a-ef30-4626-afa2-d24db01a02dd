package com.jygjexp.jynx.tms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.tms.es.document.AddressDocument;
import com.jygjexp.jynx.tms.entity.TmsBaseCityStreetPostcodeEntity;
import com.jygjexp.jynx.tms.mapper.TmsBaseCityStreetPostcodeMapper;
import com.jygjexp.jynx.tms.service.AddressSearchService;
import com.jygjexp.jynx.tms.service.TmsBaseCityStreetPostcodeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 加拿大邮编地址库服务实现类
 *
 * <AUTHOR>
 * @date 2025-09-02 16:17:40
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TmsBaseCityStreetPostcodeServiceImpl extends ServiceImpl<TmsBaseCityStreetPostcodeMapper, TmsBaseCityStreetPostcodeEntity> implements TmsBaseCityStreetPostcodeService {

    private final TmsBaseCityStreetPostcodeMapper tmsBaseCityStreetPostcodeMapper;
    private final AddressSearchService addressSearchService;

    /**
     * 是否启用Elasticsearch搜索
     */
    @Value("${address.search.elasticsearch.enabled:false}")
    private boolean elasticsearchEnabled;

    /**
     * 地址自动补全搜索
     *
     * @param keyword 搜索关键词（支持邮编/城市/街道）
     * @param limit 返回结果的最大条数
     * @return 匹配的地址列表
     */
    public List<TmsBaseCityStreetPostcodeEntity> searchAddressAutocomplete(String keyword, Integer limit) {
        if (StrUtil.isBlank(keyword)) {
            return Collections.emptyList();
        }

        // 限制返回结果数量，避免过多数据
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        if (limit > 50) {
            limit = 50; // 最大限制50条
        }

        // 如果启用了Elasticsearch，优先使用ES搜索
        if (elasticsearchEnabled) {
            try {
                List<AddressDocument> esResults = addressSearchService.searchAddressWithElasticsearch(keyword, limit);
                if (!esResults.isEmpty()) {
                    // 将ES结果转换为实体对象
                    return convertDocumentsToEntities(esResults);
                }
                log.warn("Elasticsearch搜索无结果，降级到数据库搜索，关键词: {}", keyword);
            } catch (Exception e) {
                log.error("Elasticsearch搜索失败，降级到数据库搜索，关键词: {}, 错误: {}", keyword, e.getMessage());
            }
        }

        // 使用数据库搜索（降级方案）
        return searchFromDatabase(keyword, limit);
    }

    /**
     * 从数据库搜索（降级方案）
     */
    private List<TmsBaseCityStreetPostcodeEntity> searchFromDatabase(String keyword, Integer limit) {
        // 构建查询条件
        LambdaQueryWrapper<TmsBaseCityStreetPostcodeEntity> wrapper = new LambdaQueryWrapper<>();

        // 只查询有效状态的数据
        wrapper.eq(TmsBaseCityStreetPostcodeEntity::getStatus, 1);

        // 关键词搜索：支持邮编、城市、街道的模糊匹配
        String searchKeyword = keyword.trim().toUpperCase();

        wrapper.and(w -> w
            // 三字邮编前缀匹配（优先级最高）
            .likeRight(TmsBaseCityStreetPostcodeEntity::getThreePostCode, searchKeyword)
            // 六字邮编前缀匹配
            .or().likeRight(TmsBaseCityStreetPostcodeEntity::getSixPostCode, searchKeyword)
            // 城市名称模糊匹配
            .or().like(TmsBaseCityStreetPostcodeEntity::getCity, searchKeyword)
            // 街道名称模糊匹配
            .or().like(TmsBaseCityStreetPostcodeEntity::getRstreet, searchKeyword)
        );

        // 排序：优先显示邮编匹配的结果，然后按城市、街道排序
        wrapper.orderByAsc(TmsBaseCityStreetPostcodeEntity::getThreePostCode)
               .orderByAsc(TmsBaseCityStreetPostcodeEntity::getCity)
               .orderByAsc(TmsBaseCityStreetPostcodeEntity::getRstreet);

        // 限制返回数量
        wrapper.last("LIMIT " + limit);

        return this.list(wrapper);
    }

    /**
     * 将Elasticsearch文档转换为实体对象
     */
    private List<TmsBaseCityStreetPostcodeEntity> convertDocumentsToEntities(List<AddressDocument> documents) {
        return documents.stream().map(doc -> {
            TmsBaseCityStreetPostcodeEntity entity = new TmsBaseCityStreetPostcodeEntity();
            entity.setId(Integer.valueOf(doc.getId()));
            entity.setThreePostCode(doc.getThreePostCode());
            entity.setSixPostCode(doc.getSixPostCode());
            entity.setCity(doc.getCity());
            entity.setProvince(doc.getProvince());
            entity.setRstreet(doc.getStreet());
            entity.setStatus(doc.getStatus());
            return entity;
        }).collect(Collectors.toList());
    }

    /**
     * 同步数据到Elasticsearch
     * 将数据库中的地址数据同步到ES索引中
     */
    public void syncDataToElasticsearch() {
        if (!elasticsearchEnabled) {
            log.warn("Elasticsearch未启用，跳过数据同步");
            return;
        }

        try {
            log.info("开始同步地址数据到Elasticsearch...");

            // 查询所有有效的地址数据
            LambdaQueryWrapper<TmsBaseCityStreetPostcodeEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TmsBaseCityStreetPostcodeEntity::getStatus, 1);
            List<TmsBaseCityStreetPostcodeEntity> entities = this.list(wrapper);

            if (entities.isEmpty()) {
                log.warn("没有找到有效的地址数据");
                return;
            }

            // 先清空现有索引
            addressSearchService.deleteAllDocuments();

            // 转换为ES文档并批量保存
            List<AddressDocument> documents = entities.stream()
                    .map(addressSearchService::convertToDocument)
                    .collect(Collectors.toList());

            // 分批保存，避免一次性处理过多数据
            int batchSize = 1000;
            for (int i = 0; i < documents.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, documents.size());
                List<AddressDocument> batch = documents.subList(i, endIndex);
                addressSearchService.saveDocuments(batch);
                log.info("已同步第{}批数据，共{}条", (i / batchSize) + 1, batch.size());
            }

            log.info("地址数据同步完成，共同步{}条记录", entities.size());

        } catch (Exception e) {
            log.error("同步数据到Elasticsearch失败: {}", e.getMessage(), e);
            throw new RuntimeException("数据同步失败", e);
        }
    }
}
