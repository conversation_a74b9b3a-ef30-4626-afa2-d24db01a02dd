package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.entity.TmsOrderFeeDetailsEntity;

import java.math.BigDecimal;

public interface TmsOrderFeeDetailsService extends IService<TmsOrderFeeDetailsEntity> {

    void saveOrderFeeDetails(String orderNo, BigDecimal forecastBaseFreight,BigDecimal forecastTax,BigDecimal forecastAdditionalFee,BigDecimal forecastTotalFee);

}