package com.jygjexp.jynx.tms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.tms.constants.StoreEnums;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderExceptionEntity;
import com.jygjexp.jynx.tms.mapper.TmsStoreOrderExceptionMapper;
import com.jygjexp.jynx.tms.mapper.TmsStoreOrderMapper;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.utils.SelectUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 快递订单异常处理表
 *
 * <AUTHOR>
 * @date 2025-07-21 11:47:35
 */
@Service
@RequiredArgsConstructor
public class TmsStoreOrderExceptionServiceImpl extends ServiceImpl<TmsStoreOrderExceptionMapper, TmsStoreOrderExceptionEntity> implements TmsStoreOrderExceptionService {
    private final TmsStoreOrderMapper tmsStoreOrderMapper;
    private final TmsStoreMessageTraceService tmsStoreMessageTraceService;
    private final StoreOrderEmailSendService emailSendAsyncService;

    @Override
    public List<TmsStoreOrderExceptionEntity> getStoreOrderExceptionsByStoreOrderId(Long id) {
        return this.baseMapper.selectList(Wrappers.<TmsStoreOrderExceptionEntity>lambdaQuery()
                .eq(TmsStoreOrderExceptionEntity::getOrderId, id));
    }

    @Override
    public void saveStoreMessageTrace(TmsStoreOrderExceptionEntity tmsStoreOrderException) {
        // 通过主单查询客户ID
        TmsStoreOrderEntity mainOrder = tmsStoreOrderMapper.selectOne(Wrappers.<TmsStoreOrderEntity>lambdaQuery()
                .eq(TmsStoreOrderEntity::getId, tmsStoreOrderException.getOrderId()));
        // 查询对应的子单号
        List<TmsStoreOrderEntity> subOrders = tmsStoreOrderMapper.selectList(Wrappers.<TmsStoreOrderEntity>lambdaQuery()
                .likeRight(TmsStoreOrderEntity::getEntrustedOrderNumber, mainOrder.getEntrustedOrderNumber())
                .eq(TmsStoreOrderEntity::getSubFlag, Boolean.TRUE));
        List<String> subOrderList = subOrders.stream().map(TmsStoreOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());
        tmsStoreMessageTraceService.saveStoreMessageTrace(mainOrder.getStoreCustomerId(), StoreEnums.StoreMessageTrace.BusinessSubType.ORDER_EXCEPTION.getValue(), subOrderList);

        // 发送异常邮件
        if(SelectUtil.checkNoticeOpen(mainOrder.getStoreCustomerId(), StoreEnums.StoreOrderEmail.EmailType.ORDER_EXCEPTION)) {
            emailSendAsyncService.sendStoreOrderEmail(SelectUtil.buildEmailTemplateDTO(mainOrder.getId()), StoreEnums.StoreOrderEmail.EmailType.ORDER_EXCEPTION);
        }
    }
}