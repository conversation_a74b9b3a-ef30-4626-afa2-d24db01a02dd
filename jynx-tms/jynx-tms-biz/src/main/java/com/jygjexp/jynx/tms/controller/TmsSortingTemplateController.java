package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.dto.TmsGridAndSortingTemplateDto;
import com.jygjexp.jynx.tms.dto.TmsSortingTemplateDto;
import com.jygjexp.jynx.tms.dto.TmsSortingTemplatePageDto;
import com.jygjexp.jynx.tms.entity.TmsSortingTemplateEntity;
import com.jygjexp.jynx.tms.request.ConditionGroup;
import com.jygjexp.jynx.tms.request.Order;
import com.jygjexp.jynx.tms.service.TmsSortingRuleService;
import com.jygjexp.jynx.tms.service.TmsSortingTemplateService;
import com.jygjexp.jynx.tms.utils.BuildSpElUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分拣模版表
 *
 * <AUTHOR>
 * @date 2025-06-12 19:58:08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsSortingTemplate" )
@Tag(description = "tmsSortingTemplate" , name = "分拣模版表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsSortingTemplateController {

    private final  TmsSortingTemplateService tmsSortingTemplateService;
    private final TmsSortingRuleService tmsSortingRuleService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsSortingTemplatePageDto 分拣模版表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsSortingTemplate_view')" )
    public R getTmsSortingTemplatePage(Page page, TmsSortingTemplatePageDto tmsSortingTemplatePageDto) {
        return tmsSortingTemplateService.pageSearch(page,tmsSortingTemplatePageDto);
    }


    /**
     * 新增分拣模版表
     * @param tmsSortingTemplateDto 分拣模版表
     * @return R
     */
    @Operation(summary = "新增分拣模版表" , description = "新增分拣模版表" )
    @SysLog("新增分拣模版表" )
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('tms_tmsSortingTemplate_add')" )
    public R add(@RequestBody TmsSortingTemplateDto tmsSortingTemplateDto) {
        return tmsSortingTemplateService.add(tmsSortingTemplateDto);
    }


    /**
     * 路线号分页数据
     */
    @Operation(summary = "路线号分页数据" , description = "路线号分页数据" )
    @PostMapping("/getRouteNumberPage" )
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingTemplate_route')" )
    public R getRouteNumberPage(Page page,String routeNumber,Long warehouseNameId) {
        return tmsSortingTemplateService.getRouteNumberPage(page,routeNumber,warehouseNameId);
    }


    /**
     * 所有路线号数据
     */
    @Operation(summary = "所有路线号数据" , description = "所有路线号数据" )
    @GetMapping("/getAllRoute")
    public R getAllRouteNumber() {
        return tmsSortingTemplateService.getAllRouteNumber();
    }



    /**
     * 根据id获取模版的相关信息
     */
    @Operation(summary = "根据id获取模版的相关信息" , description = "根据id获取模版的相关信息" )
    @GetMapping("/getSortingTemplateById/{id}")
    public R getSortingTemplateById(@PathVariable Long id) {
        return tmsSortingTemplateService.getSortingTemplateById(id);
    }


    /**
     * 获取此时时启用的唯一的分拣模版
     */
    @Inner(value = false)
    @Operation(summary = "获取此时时启用的唯一的分拣模版" , description = "获取此时时启用的唯一的分拣模版" )
    @GetMapping("/getEnableSortingTemplate")
    public TmsSortingTemplateDto getEnableSortingTemplate() {
        return tmsSortingTemplateService.getEnableSortingTemplate();
    }

    /**
     * 修改分拣模版表
     * @param tmsSortingTemplateDto 分拣模版表
     * @return R
     */
    @Operation(summary = "修改分拣模版表" , description = "修改分拣模版表" )
    @SysLog("修改分拣模版表" )
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('tms_tmsSortingTemplate_edit')" )
    public R updateRuleById(@RequestBody TmsSortingTemplateDto tmsSortingTemplateDto) {
        return tmsSortingTemplateService.updateRuleById(tmsSortingTemplateDto);
    }

    /**
     * 启用或者禁用
     * @param id 启用或者禁用
     * @return R
     */
    @Operation(summary = "启用或者禁用" , description = "启用或者禁用" )
    @SysLog("启用或者禁用" )
    @PutMapping("/change_enable")
//    @PreAuthorize("@pms.hasPermission('tms_tmsSortingTemplate_edit')" )
    public R changeEnable(@RequestParam(value = "id",required = true) Long id,
                            @RequestParam(value = "isEnable",required = true) Integer isEnable,
                          @RequestParam(value = "isNewBatch",required = true) Integer isNewBatch) {
        return R.ok(tmsSortingTemplateService.changeEnable(id,isEnable,isNewBatch));
    }

    /**
     * 判断当日批次是否生成过
     */
    @Operation(summary = "判断当日批次是否生成过" , description = "判断当日批次是否生成过" )
    @GetMapping("/isGenerateBatch")
    public R isGenerateBatch() {
        return tmsSortingTemplateService.isGenerateBatch() ? R.ok(Boolean.TRUE) : R.ok(Boolean.FALSE);
    }

    /**
     * 查询格口-模版的对应数据
     */
    @Operation(summary = "查询格口-模版的对应数据" , description = "查询格口-模版的对应数据" )
    @GetMapping("/getGridAndTemplate" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsSortingTemplate_view')" )
    public R getGridAndTemplate() {
        return tmsSortingTemplateService.getGridAndTemplate();
    }

    /**
     * 格口维护-新增
     */
    @Operation(summary = "格口维护-新增" , description = "格口维护-新增" )
    @PostMapping("/addGridAndSortingTemplate" )
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingTemplate_gt')" )
    public R addGridAndSortingTemplate(@RequestBody List<TmsGridAndSortingTemplateDto> tmsGridAndSortingTemplateDtos) {
        return tmsSortingTemplateService.addGridAndSortingTemplate(tmsGridAndSortingTemplateDtos);
    }


    /**
     * 通过id删除分拣模版表
     * @param id
     * @return R
     */
    @Operation(summary = "通过id删除分拣模版表" , description = "通过id删除分拣模版表" )
    @SysLog("通过id删除分拣模版表" )
    @DeleteMapping("{id}")
//    @PreAuthorize("@pms.hasPermission('tms_tmsSortingTemplate_del')" )
    public R removeTemplate(@PathVariable Long id) {
        return R.ok(tmsSortingTemplateService.removeTemplate(id));
    }


    /**
     * 导出excel 表格
     * @param tmsSortingTemplate 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingTemplate_export')" )
    public List<TmsSortingTemplateEntity> export(TmsSortingTemplateEntity tmsSortingTemplate,Long[] ids) {
        return tmsSortingTemplateService.list(Wrappers.lambdaQuery(tmsSortingTemplate).in(ArrayUtil.isNotEmpty(ids), TmsSortingTemplateEntity::getId, ids));
    }


    /**
     * 测试转Spel表达式
     * @param conditionGroup 测试转Spel表达式
     * @return R
     */
    @Inner(value = false)
    @Operation(summary = "测试转Spel表达式" , description = "测试转Spel表达式" )
    @SysLog("测试转Spel表达式" )
    @PostMapping("test")
//    @PreAuthorize("@pms.hasPermission('tms_tmsSortingTemplate_add')" )
    public R save(@RequestBody ConditionGroup conditionGroup) {
        String jsonString = JSON.toJSONString(conditionGroup);
        // 构建 SpEL 表达式
        JSONObject root = JSON.parseObject(jsonString);
        String spel = BuildSpElUtil.buildSpEL(root);


        Order order = new Order();
        order.setWeight(new BigDecimal(15));
        order.setCity("88");
        order.setMerchantCode("X456");
        order.setDeliveryCompany("顺丰");
        boolean b = BuildSpElUtil.evaluateSpEL(spel, order);

        System.out.println(b);
        System.out.println("SpEL表达式"+spel);
        return R.ok(spel);
    }

}
