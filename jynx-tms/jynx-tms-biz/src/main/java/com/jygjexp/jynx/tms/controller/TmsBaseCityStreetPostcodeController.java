package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsBaseCityStreetPostcodeEntity;
import com.jygjexp.jynx.tms.service.TmsBaseCityStreetPostcodeService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 加拿大邮编地址库
 *
 * <AUTHOR>
 * @date 2025-09-02 16:17:40
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsBaseCityStreetPostcode" )
@Tag(description = "tmsBaseCityStreetPostcode" , name = "加拿大邮编地址库管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsBaseCityStreetPostcodeController {

    private final  TmsBaseCityStreetPostcodeService tmsBaseCityStreetPostcodeService;

/*    *//**
     * 分页查询
     * @param page 分页对象
     * @param tmsBaseCityStreetPostcode 加拿大邮编地址库
     * @return
     *//*
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsBaseCityStreetPostcode_view')" )
    public R getTmsBaseCityStreetPostcodePage(@ParameterObject Page page, @ParameterObject TmsBaseCityStreetPostcodeEntity tmsBaseCityStreetPostcode) {
        LambdaQueryWrapper<TmsBaseCityStreetPostcodeEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsBaseCityStreetPostcodeService.page(page, wrapper));
    }


    *//**
     * 通过id查询加拿大邮编地址库
     * @param id id
     * @return R
     *//*
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsBaseCityStreetPostcode_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(tmsBaseCityStreetPostcodeService.getById(id));
    }

    *//**
     * 新增加拿大邮编地址库
     * @param tmsBaseCityStreetPostcode 加拿大邮编地址库
     * @return R
     *//*
    @Operation(summary = "新增加拿大邮编地址库" , description = "新增加拿大邮编地址库" )
    @SysLog("新增加拿大邮编地址库" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBaseCityStreetPostcode_add')" )
    public R save(@RequestBody TmsBaseCityStreetPostcodeEntity tmsBaseCityStreetPostcode) {
        return R.ok(tmsBaseCityStreetPostcodeService.save(tmsBaseCityStreetPostcode));
    }

    *//**
     * 修改加拿大邮编地址库
     * @param tmsBaseCityStreetPostcode 加拿大邮编地址库
     * @return R
     *//*
    @Operation(summary = "修改加拿大邮编地址库" , description = "修改加拿大邮编地址库" )
    @SysLog("修改加拿大邮编地址库" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBaseCityStreetPostcode_edit')" )
    public R updateById(@RequestBody TmsBaseCityStreetPostcodeEntity tmsBaseCityStreetPostcode) {
        return R.ok(tmsBaseCityStreetPostcodeService.updateById(tmsBaseCityStreetPostcode));
    }

    *//**
     * 通过id删除加拿大邮编地址库
     * @param ids id列表
     * @return R
     *//*
    @Operation(summary = "通过id删除加拿大邮编地址库" , description = "通过id删除加拿大邮编地址库" )
    @SysLog("通过id删除加拿大邮编地址库" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBaseCityStreetPostcode_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(tmsBaseCityStreetPostcodeService.removeBatchByIds(CollUtil.toList(ids)));
    }*/


    /**
     * 导出excel 表格
     * @param tmsBaseCityStreetPostcode 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
/*    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsBaseCityStreetPostcode_export')" )
    public List<TmsBaseCityStreetPostcodeEntity> export(TmsBaseCityStreetPostcodeEntity tmsBaseCityStreetPostcode,Integer[] ids) {
        return tmsBaseCityStreetPostcodeService.list(Wrappers.lambdaQuery(tmsBaseCityStreetPostcode).in(ArrayUtil.isNotEmpty(ids), TmsBaseCityStreetPostcodeEntity::getId, ids));
    }*/
}