package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.jygjexp.jynx.tms.constants.StoreEnums;
import com.jygjexp.jynx.tms.dto.StoreOrderEmailTemplateDTO;
import com.jygjexp.jynx.tms.service.StoreOrderEmailSendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Properties;

/**
 * 订单邮件发送服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class StoreOrderEmailSendServiceImpl implements StoreOrderEmailSendService {

    @Autowired
    private TemplateEngine templateEngine;

    private static final String HOST = "smtp.larksuite.com";
    private static final String USER = "<EMAIL>";
    private static final String PASSWORD = "paXpbkG8zG2R0ODB";

    @Async
    @Override
    public void sendStoreOrderEmail(StoreOrderEmailTemplateDTO storeOrder, StoreEnums.StoreOrderEmail.EmailType orderEmailStatus) {
        try {
            switch (orderEmailStatus) {
                case ORDER_CREATE:
                    sendOrderCreateEmail(storeOrder.getSendEmail(), storeOrder);
                    break;
                case ORDER_VERIFIED:
                    sendOrderVerifiedEmail(storeOrder.getSendEmail(), storeOrder);
                    break;
                case ORDER_SHIPPING:
                    sendShippingEmail(storeOrder.getSendEmail(), storeOrder);
                    break;
                case ORDER_CONFIRMATION:
                    sendOrderConfirmationEmail(storeOrder.getSendEmail(), storeOrder);
                    break;
                case ORDER_EXCEPTION:
                    sendOrderExceptionEmail(storeOrder.getSendEmail(), storeOrder);
                    break;
                case ORDER_CANCEL:
                    sendCancelEmail(storeOrder.getSendEmail(), storeOrder);
                    break;
                default:
                    log.warn("未知的邮件类型: {}", orderEmailStatus);
                    break;
            }
        } catch (Exception e) {
            log.error("发送订单邮件失败", e);
        }
    }

    /**
     * 发送邮件核心方法
     */
    private boolean sendMail(String email, String subject, String content) {
        Long startTime = System.currentTimeMillis();

        Properties props = new Properties();
        props.put("mail.smtp.host", HOST);
        props.put("mail.smtp.socketFactory.port", "465");
        props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.port", "465");

        Session session = Session.getDefaultInstance(props, new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(USER, PASSWORD);
            }
        });

        try {
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(USER));
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(email));
            message.setSubject(subject);
            message.setContent(content, "text/html; charset=utf-8");

            Transport.send(message);

            Long endTime = System.currentTimeMillis();
            log.info("邮件发送成功！耗时：{}ms，当前线程：{}",
                    (endTime - startTime), Thread.currentThread().getName());
            return true;

        } catch (MessagingException e) {
            log.error("邮件发送失败", e);
            return false;
        }
    }

    /**
     * 使用Thymeleaf模板发送邮件
     */
    private boolean sendMailWithTemplate(String email, String subject, String templateName, Object data) {
        if (StrUtil.isBlank(email)) {
            return false;
        }

        try {
            Context context = new Context();
            context.setVariable("data", data);
            String htmlContent = templateEngine.process(templateName, context);

            return sendMail(email, subject, htmlContent);
        } catch (Exception e) {
            log.error("Template processing failed", e);
            return false;
        }
    }

    /**
     * 订单取消邮件发送
     */
    private boolean sendCancelEmail(String email, StoreOrderEmailTemplateDTO data) {
        return sendMailWithTemplate(email, "Your order has been cancelled – Neighbour Express",
                "order_cancel", data);
    }

    /**
     * 订单签收邮件发送
     */
    private boolean sendOrderConfirmationEmail(String email, StoreOrderEmailTemplateDTO data) {
        return sendMailWithTemplate(email, "Your package has been delivered – Neighbour Express",
                "order_confirmation", data);
    }

    /**
     * 订单异常邮件发送
     */
    private boolean sendOrderExceptionEmail(String email, StoreOrderEmailTemplateDTO data) {
        return sendMailWithTemplate(email, "Important Notice: Issue with Your Order – Neighbour Express",
                "order_exception", data);
    }

    /**
     * 订单核销邮件发送
     */
    private boolean sendOrderVerifiedEmail(String email, StoreOrderEmailTemplateDTO data) {
        return sendMailWithTemplate(email, "Your package is ready for delivery – Verified by Neighbour Express",
                "order_verified", data);
    }

    /**
     * 订单配送邮件发送
     */
    private boolean sendShippingEmail(String email, StoreOrderEmailTemplateDTO data) {
        return sendMailWithTemplate(email, "Your package is on the way – Neighbour Express",
                "order_shipping", data);
    }

    /**
     * 订单创建邮件发送
     */
    private boolean sendOrderCreateEmail(String email, StoreOrderEmailTemplateDTO data) {
        return sendMailWithTemplate(email, "Your order has been placed successfully – Neighbour Express",
                "order_create", data);
    }
}
