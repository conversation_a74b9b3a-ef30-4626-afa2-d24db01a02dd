package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.mapper.*;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.utils.SpelExpressionEvaluator;
import com.jygjexp.jynx.tms.vo.*;
import com.jygjexp.jynx.tms.vo.excel.TmsReceivableExcelVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
/**
 * 财务应收信息表
 *
 * <AUTHOR>
 * @date 2025-07-15 18:18:47
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TmsReceivableServiceImpl extends ServiceImpl<TmsReceivableMapper, TmsReceivableEntity> implements TmsReceivableService {

    private final TmsReceivableMapper tmsReceivableMapper;
    private final TmsServiceProviderService tmsServiceProviderService;
    private final TmsServiceQuoteService tmsServiceQuoteService;
    private final TmsServiceRegionDetailService tmsServiceRegionDetailService;
    private final TmsServiceQuotePriceService tmsServiceQuotePriceService;
    private final TmsCargoInfoMapper cargoInfoMapper;
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final TmsCustomerMapper customerMapper;
    private final TmsSortingRecordService tmsSortingRecordService;
    private final TmsFeeRuleMapper tmsFeeRuleMapper;
    private final TmsFeeRuleService tmsFeeRuleService;
    private final TmsFeeRuleExprMapper tmsFeeRuleExprMapper;
    private final TmsOrderLogService tmsOrderLogService;
    private final TmsTaxRateService tmsTaxRateService;

    // 分页查询
    @Override
    public Page<TmsReceivablePageVo> search(Page page, TmsReceivablePageVo vo) {
        // 查询所有数据
        MPJLambdaWrapper<TmsReceivableEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsReceivableEntity.class)
                .select(TmsCustomerEntity::getCustomerNameCn)
                .select(TmsCustomerOrderEntity::getForecastedPrice)
                .select(TmsCustomerOrderEntity::getReceiverName)
                .select(TmsCustomerOrderEntity::getDestAddress)
                .select(TmsCustomerOrderEntity::getForecastedPrice)
                .leftJoin(TmsCustomerEntity.class, TmsCustomerEntity::getId, TmsReceivableEntity::getCustomerId)
                .leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getEntrustedOrderNumber, TmsReceivableEntity::getEntrustedOrderNo)
                .eq(TmsReceivableEntity::getSubFlag, 0) // 只查询主单
                .between(StrUtil.isNotBlank(vo.getStartTime()) && StrUtil.isNotBlank(vo.getEndTime()), TmsReceivableEntity::getCreateTime, vo.getStartTime(), vo.getEndTime())
                .eq(ObjectUtil.isNotNull(vo.getCustomerId()), TmsReceivableEntity::getCustomerId, vo.getCustomerId())
                .orderByDesc(TmsReceivableEntity::getCreateTime);

        if (StrUtil.isNotBlank(vo.getEntrustedOrderNo())) {
            wrapper.like(TmsReceivableEntity::getEntrustedOrderNo, vo.getEntrustedOrderNo());
        }

        return  tmsReceivableMapper.selectJoinPage(page,TmsReceivablePageVo.class, wrapper);
    }

    // 根据id查询详情
    @Override
    public TmsReceivableDetailVo getDetail(String id) {
        // 组装返回数据
        TmsReceivableDetailVo detailVo = new TmsReceivableDetailVo();
        TmsReceivableEntity main = tmsReceivableMapper.selectById(id);

        if (main.getSurchargeDetail() != null) {
            // 查询附加费明细列表
            List<String> surchargeDetailList = Arrays.stream(main.getSurchargeDetail().trim().split(","))
                    .map(String::trim)
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(surchargeDetailList)) {
                List<TmsFeeRuleEntity> tmsFeeRuleEntities = tmsFeeRuleMapper.selectList(new LambdaQueryWrapper<TmsFeeRuleEntity>()
                        .in(TmsFeeRuleEntity::getId, surchargeDetailList)
                        .eq(TmsFeeRuleEntity::getIsValid, 1));
                detailVo.setFeeRuleList(tmsFeeRuleEntities);
            }
        }
        detailVo.setMain(main);
        return detailVo;
    }

    // 导出应收记录
    @Override
    public List<TmsReceivableExcelVo> getExcel(TmsReceivablePageVo vo, Long[] ids) {
        log.info("开始导出应收记录Excel，查询条件: {}, 指定ID: {}", vo, ids);

        // 查询所有数据
        MPJLambdaWrapper<TmsReceivableEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsReceivableEntity.class)
                .select(TmsCustomerEntity::getCustomerNameCn)
                .select(TmsCustomerOrderEntity::getForecastedPrice)
                .select(TmsCustomerOrderEntity::getReceiverName)
                .select(TmsCustomerOrderEntity::getDestAddress)
                .select(TmsCustomerOrderEntity::getForecastedPrice)
                .leftJoin(TmsCustomerEntity.class, TmsCustomerEntity::getId, TmsReceivableEntity::getCustomerId)
                .leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getEntrustedOrderNumber, TmsReceivableEntity::getEntrustedOrderNo)
                .eq(TmsReceivableEntity::getSubFlag, 0) // 只查询主单
                .between(StrUtil.isNotBlank(vo.getStartTime()) && StrUtil.isNotBlank(vo.getEndTime()), TmsReceivableEntity::getCreateTime, vo.getStartTime(), vo.getEndTime())
                .eq(ObjectUtil.isNotNull(vo.getCustomerId()), TmsReceivableEntity::getCustomerId, vo.getCustomerId())
                .orderByDesc(TmsReceivableEntity::getCreateTime);

        if (StrUtil.isNotBlank(vo.getEntrustedOrderNo())) {
            wrapper.like(TmsReceivableEntity::getEntrustedOrderNo, vo.getEntrustedOrderNo());
        }

        return tmsReceivableMapper.selectJoinList(TmsReceivableExcelVo.class, wrapper);
    }

    /**
     * 价格计算 - 基于订单数据计算基础费用价格
     */
    @Override
    public PriceCalculationResultVo calculatePrice(PriceCalculationRequestVo request) {
        PriceCalculationResultVo result = new PriceCalculationResultVo();
        List<PriceCalculationDetailVo> details = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;
        BigDecimal totalPrice = BigDecimal.ZERO;

        for (TmsOrderPriceCalculationVo orderVo : request.getOrders()) {
            // 价格计算封装方法
            PriceCalculationDetailVo detail = calculateSingleOrderPrice(orderVo, request.getProviderName(), request.getSourceChannel());
            details.add(detail);

            if (detail.getSuccess()) {
                successCount++;
                if (detail.getFinalPrice() != null) {
                    totalPrice = totalPrice.add(detail.getFinalPrice());
                }
            } else {
                failureCount++;
            }
        }

        result.setDetails(details);
        result.setSuccessCount(successCount);
        result.setFailureCount(failureCount);
        result.setTotalPrice(totalPrice);

        log.info("价格计算完成，成功: {}, 失败: {}, 总价格: {}", successCount, failureCount, totalPrice);
        return result;
    }

    /**
     * 计算单个订单的价格
     * 计算流程：服务商匹配 -> 区域确定 -> 价格计算 -> 附加费计算
     *
     * @param orderVo 订单价格计算VO
     * @param providerName 服务商名称
     * @param sourceChannel 来源渠道：1-询价，2-分拣计算价格
     */
    private PriceCalculationDetailVo calculateSingleOrderPrice(TmsOrderPriceCalculationVo orderVo, String providerName, Integer sourceChannel) {
        PriceCalculationDetailVo detail = new PriceCalculationDetailVo();
        // todo: 用于询价，如果没有提供体积重则需要计算体积重
        if (sourceChannel == 1) {
            // 获取客户的体积重系数
            TmsCustomerEntity customer = customerMapper.selectById(orderVo.getCustomerId());

            if (null == customer) {
                detail.setSuccess(false);
                detail.setErrorMessage("未查找到询价客户信息");
                return detail;
            }

            if (customer.getVolumeCoefficient() != null && customer.getVolumeCoefficient() > 0) {
                // 体积重 = 体积（cm³） ÷ 客户材积重系数
                BigDecimal volumeWeight = orderVo.getTotalVolume().divide(new BigDecimal(customer.getVolumeCoefficient()), 2, RoundingMode.HALF_UP) // 保留2位小数先
                        .setScale(0, RoundingMode.CEILING); // 向上取整为整数
                orderVo.setVolumeWeight(volumeWeight);
            }
        }

        // 设置基本订单信息
        detail.setOrderId(orderVo.getId());
        detail.setCustomerOrderNumber(orderVo.getCustomerOrderNumber());
        detail.setEntrustedOrderNumber(orderVo.getEntrustedOrderNumber());
        detail.setOrderWeight(orderVo.getTotalWeight());
        detail.setShipperPostalCode(orderVo.getShipperPostalCode());
        detail.setDestPostalCode(orderVo.getDestPostalCode());
        detail.setShipperCity(extractCityFromAddress(orderVo.getOrigin()));
        detail.setDestCity(extractCityFromAddress(orderVo.getDestination()));
        detail.setVolume(orderVo.getTotalVolume());
        detail.setCustomerId(orderVo.getCustomerId());
        detail.setWeight(orderVo.getTotalWeight());

        try {
            // ========== 服务商和区域匹配 ==========
            // 先查找服务商
            TmsServiceProviderEntity provider = tmsServiceProviderService.getOne(
                new LambdaQueryWrapper<TmsServiceProviderEntity>()
                    .eq(TmsServiceProviderEntity::getProviderCode, providerName)
                    .eq(TmsServiceProviderEntity::getIsValid, 1)
            );

            if (provider == null) {
                detail.setSuccess(false);
                detail.setErrorMessage("未找到服务商: " + providerName);

                // 记录失败日志（仅当sourceChannel=2时记录）
                if (sourceChannel != null && sourceChannel == 2) {
                    String errorMessage = "价格计算失败，未找到服务商: " + providerName;
                    tmsOrderLogService.saveLog(orderVo.getEntrustedOrderNumber(), 404, errorMessage, "");
                }
                return detail;
            }

            // 格式化发货地邮政编码（去除空格、全部转为大写）（目前使用6位范围匹配）
            String shipperPostalCode = normalizeFullPostalCode(orderVo.getShipperPostalCode());
            if (StrUtil.isBlank(shipperPostalCode)) {
                detail.setSuccess(false);
                detail.setErrorMessage("无效的发货地邮政编码: " + orderVo.getShipperPostalCode());

                // 记录失败日志（仅当sourceChannel=2时记录）
                if (sourceChannel != null && sourceChannel == 2) {
                    String errorMessage = "价格计算失败，无效的发货地邮政编码: " + orderVo.getShipperPostalCode();
                    tmsOrderLogService.saveLog(orderVo.getEntrustedOrderNumber(), 404, errorMessage, "");
                }
                return detail;
            }
            // 设置前3位用于显示
            detail.setMatchedPostalPrefix(shipperPostalCode.substring(0, Math.min(3, shipperPostalCode.length())));

            // 根据服务商id查找匹配的服务报价
            List<TmsServiceQuoteEntity> quotes = tmsServiceQuoteService.list(
                new LambdaQueryWrapper<TmsServiceQuoteEntity>()
                    .eq(TmsServiceQuoteEntity::getProviderId, provider.getProviderId())
                    .eq(TmsServiceQuoteEntity::getIsValid, 1)
            );

            if (CollUtil.isEmpty(quotes)) {
                detail.setSuccess(false);
                detail.setErrorMessage("未找到服务商所配置的报价规则: " + providerName);

                // 记录失败日志（仅当sourceChannel=2时记录）
                if (sourceChannel != null && sourceChannel == 2) {
                    String errorMessage = "价格计算失败，未找到服务商所配置的报价规则: " + providerName;
                    tmsOrderLogService.saveLog(orderVo.getEntrustedOrderNumber(), 404, errorMessage, "");
                }
                return detail;
            }

            // ========== 纵区判断逻辑 ==========
            // 确定纵区(如果存在多个纵区则根据订单发货、到货邮编和发货、收货地判断是否跨城市)
            TmsZoneMatchResultVo zoneResult = determineZoneWithMultiRegionCheck(quotes,
                orderVo.getShipperPostalCode(), orderVo.getDestPostalCode(),
                orderVo.getOrigin(), orderVo.getDestination());

            if (!zoneResult.isSuccess()) {
                detail.setSuccess(false);
                detail.setErrorMessage(zoneResult.getErrorMessage());

                // 记录失败日志（仅当sourceChannel=2时记录）
                if (sourceChannel != null && sourceChannel == 2) {
                    String errorMessage = "价格计算失败，区域匹配失败: " + zoneResult.getErrorMessage();
                    tmsOrderLogService.saveLog(orderVo.getEntrustedOrderNumber(), 404, errorMessage, "");
                }
                return detail;
            }

            detail.setMatchedProviderName(providerName);
            detail.setMatchedQuoteId(zoneResult.getMatchedQuote().getId());
            detail.setMatchedQuoteName(zoneResult.getMatchedQuote().getQuoteName());
            detail.setZoneType(zoneResult.getZoneType());

            // ========== 基于重量的价格计算 ==========
            BigDecimal actualWeight = orderVo.getActualWeight() != null && orderVo.getActualWeight().compareTo(BigDecimal.ZERO) > 0 ? orderVo.getActualWeight() : orderVo.getTotalWeight();
            BigDecimal volumeWeight = orderVo.getVolumeWeight() != null && orderVo.getVolumeWeight().compareTo(BigDecimal.ZERO) > 0 ? orderVo.getVolumeWeight() : orderVo.getTotalVolume();
            LocalDateTime scanTime = orderVo.getScanTime();

            // 根据客户的volumeWeightSwitch字段确定计费重量
            BigDecimal finalWeight = calculateChargeWeight(orderVo.getCustomerId(), actualWeight, volumeWeight);

            // 设置重量详细信息到返回结果中
            detail.setActualWeight(actualWeight);     // 实际重量
            detail.setVolumeWeight(volumeWeight);     // 体积重
            detail.setFinalWeightForMatching(finalWeight);  // 最终用于匹配的重量(计费重)
            detail.setScanTime(scanTime);  // 分拣时间
            if (finalWeight == null || finalWeight.compareTo(BigDecimal.ZERO) <= 0) {
                detail.setSuccess(false);
                detail.setErrorMessage("订单重量无效: " + finalWeight);
                return detail;
            }

            // 检查是否为超重订单（超过68kg）
            BigDecimal overweightThreshold = new BigDecimal("68");
            if (finalWeight.compareTo(overweightThreshold) > 0) {
                // 超重订单直接按公式计算：总重量 × 系数（Zone1为0.4，Zone2为1.9，默认0.4）
                BigDecimal coefficient = getOverweightCoefficient(zoneResult.getZoneType());
                BigDecimal basePrice = finalWeight.multiply(coefficient);

                // 应用客户等级系数
                BigDecimal customerLevelCoefficient = getCustomerLevelCoefficient(orderVo.getCustomerId());
                BigDecimal adjustedBasePrice = basePrice.multiply(customerLevelCoefficient).setScale(2, RoundingMode.HALF_UP);

                detail.setMatchedRegionName(zoneResult.getZoneType());
                detail.setMatchedWeightStart(overweightThreshold);
                detail.setMatchedWeightEnd(null); // 超重订单没有上限

                // 根据sourceChannel决定税率计算逻辑
                BigDecimal taxRate = BigDecimal.ZERO;
                if (sourceChannel != null && (sourceChannel == 2 || (sourceChannel == 1 && StrUtil.isNotBlank(orderVo.getDestination())))) {
                    // 从destination字段提取省份信息并查询税率
                    String province = extractProvinceFromDestination(orderVo.getDestination());
                    if (province != null) {
                        taxRate = getTaxRateByProvince(province);
                    }
                }
                detail.setProfitRate(taxRate); // 使用税率替代原来的利润率
                detail.setBasePrice(adjustedBasePrice); // 基础运费

                // 计算附加费
                AdditionalFeeCalculationResult additionalFeeResult = new AdditionalFeeCalculationResult();
                if (sourceChannel != null && (sourceChannel == 2 ||
                    (sourceChannel == 1 && CollUtil.isNotEmpty(orderVo.getSubOrderInfos()) && orderVo.getCargoType() != null))) {
                    //附加费计算，通过sourceChannel区分数据来源
                    additionalFeeResult = calculateAdditionalFeesWithDetail(orderVo, provider.getProviderId(), adjustedBasePrice, sourceChannel);
                    detail.setAdditionalFee(additionalFeeResult.getTotalFee());
                    detail.setSurchargeDetail(additionalFeeResult.getSurchargeDetail());
                }

                // 计算税率金额：(基础运费 + 附加费) × 税率
                BigDecimal taxAmount = BigDecimal.ZERO;
                if (taxRate.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal taxableAmount = adjustedBasePrice.add(additionalFeeResult.getTotalFee());
                    taxAmount = taxableAmount.multiply(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
                    taxAmount = taxAmount.setScale(2, RoundingMode.HALF_UP);
                    detail.setTaxFee(taxAmount);
                    log.debug("超68kg订单 {} 税率计算: 应税金额={}, 税率={}%, 税额={}", orderVo.getCustomerOrderNumber(), taxableAmount, taxRate, taxAmount);
                }

                // 计算最终价格：基础价格 + 附加费 + 税率金额
                BigDecimal finalPrice = adjustedBasePrice.add(additionalFeeResult.getTotalFee()).add(taxAmount);
                detail.setFinalPrice(finalPrice);
                detail.setSuccess(true);

                // 记录成功日志（仅当sourceChannel=2时记录）
                if (sourceChannel != null && sourceChannel == 2) {
                    String logMessage = String.format("超68kg订单价格计算成功，基础运费: %s，附加费: %s，税额: %s，总费用: %s", adjustedBasePrice, additionalFeeResult.getTotalFee(), taxAmount, finalPrice);
                    tmsOrderLogService.saveLog(orderVo.getEntrustedOrderNumber(), 205, logMessage, "");
                }

                log.debug("超68kg订单 {} 价格计算成功，重量: {}kg, 区域系数: {}, 客户等级系数: {}, 基础价格: {}, 附加费: {}, 税额: {}, 最终价格: {}",
                    orderVo.getCustomerOrderNumber(), finalWeight, coefficient, customerLevelCoefficient, adjustedBasePrice, additionalFeeResult.getTotalFee(), taxAmount, finalPrice);
                return detail;
            }

            // 正常重量订单：查找匹配的价格重量配置
            List<TmsServiceQuotePriceEntity> priceConfigs = tmsServiceQuotePriceService.list(
                    new LambdaQueryWrapper<TmsServiceQuotePriceEntity>()
                            .eq(TmsServiceQuotePriceEntity::getQuoteId, zoneResult.getMatchedQuote().getId())
                            .eq(TmsServiceQuotePriceEntity::getRegionName, zoneResult.getZoneType())
                            .eq(TmsServiceQuotePriceEntity::getIsValid, 1)
                            .le(TmsServiceQuotePriceEntity::getWeightStart, finalWeight) // start <= weight
                            .ge(TmsServiceQuotePriceEntity::getWeightEnd, finalWeight)   // end >= weight
                            .orderByAsc(TmsServiceQuotePriceEntity::getWeightStart)
            );
            // 开始匹配重量段
            TmsServiceQuotePriceEntity priceConfig = null;
            for (TmsServiceQuotePriceEntity config : priceConfigs) {
                // isWeightInRange() 检查重量是否在当前配置的范围内
                if (isWeightInRange(finalWeight, config.getWeightStart(), config.getWeightEnd())) {
                    priceConfig = config;
                    break;
                }
            }

            if (priceConfig == null) {
                detail.setSuccess(false);
                detail.setErrorMessage("未找到匹配的重量段价格配置，重量: " + finalWeight + "kg, 区域: " + zoneResult.getZoneType());

                // 记录失败日志（仅当sourceChannel=2时记录）
                if (sourceChannel != null && sourceChannel == 2) {
                    String errorMessage = String.format("价格计算失败，未找到匹配的重量段价格配置，重量: %skg, 区域: %s", finalWeight, zoneResult.getZoneType());
                    tmsOrderLogService.saveLog(orderVo.getEntrustedOrderNumber(), 404, errorMessage, "");
                }
                return detail;
            }

            // 设置匹配信息和计算最终价格
            detail.setMatchedRegionName(priceConfig.getRegionName());
            detail.setMatchedWeightStart(priceConfig.getWeightStart());
            detail.setMatchedWeightEnd(priceConfig.getWeightEnd());

            // 根据sourceChannel决定税率计算逻辑
            BigDecimal taxRate = BigDecimal.ZERO;
            if (sourceChannel != null && (sourceChannel == 2 || (sourceChannel == 1 && StrUtil.isNotBlank(orderVo.getDestination())))) {
                // 从destination字段提取省份信息并查询税率
                String province = extractProvinceFromDestination(orderVo.getDestination());
                if (province != null) {
                    taxRate = getTaxRateByProvince(province);
                }
            }
            detail.setProfitRate(taxRate); // 使用税率替代原来的利润率

            // 计算基础价格：基础价格 × 客户等级系数
            BigDecimal basePrice = calculateFinalPrice(priceConfig.getPrice(), BigDecimal.ZERO, orderVo.getCustomerId());
            detail.setBasePrice(basePrice);

            // 计算附加费
            AdditionalFeeCalculationResult additionalFeeResult = new AdditionalFeeCalculationResult();
            if (sourceChannel != null && (sourceChannel == 2 ||
                (sourceChannel == 1 && CollUtil.isNotEmpty(orderVo.getSubOrderInfos()) && orderVo.getCargoType() != null))) {
                // 统一使用现有的附加费计算方法，通过sourceChannel区分数据来源
                additionalFeeResult = calculateAdditionalFeesWithDetail(orderVo, provider.getProviderId(), basePrice, sourceChannel);
                detail.setAdditionalFee(additionalFeeResult.getTotalFee());
                detail.setSurchargeDetail(additionalFeeResult.getSurchargeDetail());
            }

            // 计算税率金额：(基础运费 + 附加费) × 税率
            BigDecimal taxAmount = BigDecimal.ZERO;
            if (taxRate.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal taxableAmount = basePrice.add(additionalFeeResult.getTotalFee());
                taxAmount = taxableAmount.multiply(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
                taxAmount = taxAmount.setScale(2, RoundingMode.HALF_UP);
                detail.setTaxFee(taxAmount);
            }

            // 计算最终价格：基础价格 + 附加费 + 税率金额
            BigDecimal finalPrice = basePrice.add(additionalFeeResult.getTotalFee()).add(taxAmount);
            detail.setFinalPrice(finalPrice);
            detail.setSuccess(true);

            // 记录成功日志（仅当sourceChannel=2时记录）
            if (sourceChannel != null && sourceChannel == 2) {
                String logMessage = String.format("价格计算成功，基础运费: %s，附加费: %s，税额: %s，总费用: %s", basePrice, additionalFeeResult.getTotalFee(), taxAmount, finalPrice);
                tmsOrderLogService.saveLog(orderVo.getEntrustedOrderNumber(), 205, logMessage, "");
            }
            log.debug("订单 {} 价格计算成功，基础价格: {}, 附加费: {}, 税额: {}, 最终价格: {}, 来源渠道: {}", orderVo.getCustomerOrderNumber(), basePrice, additionalFeeResult.getTotalFee(), taxAmount, finalPrice, sourceChannel);
        } catch (Exception e) {
            log.error("订单 {} 价格计算失败", orderVo.getCustomerOrderNumber(), e);
            detail.setSuccess(false);
            detail.setErrorMessage("价格计算异常: " + e.getMessage());
            // 记录失败日志（仅当sourceChannel=2时记录）
            if (sourceChannel != null && sourceChannel == 2) {
                String errorMessage = "价格计算失败，错误信息: " + e.getMessage();
                tmsOrderLogService.saveLog(orderVo.getEntrustedOrderNumber(), 404, errorMessage, "");
            }
        }

        return detail;
    }




    /**
     * 多纵区检查的纵区判断逻辑
     * 按照正确顺序：主要纵区匹配 -> 多纵区冲突解决 -> 单纵区直接分配
     */
    private TmsZoneMatchResultVo determineZoneWithMultiRegionCheck(List<TmsServiceQuoteEntity> quotes,
                                                                 String shipperPostalCode, String destPostalCodeParam,
                                                                 String shipperCity, String destCity) {
        try {
            // ========== 收集所有匹配的纵区 ==========
            List<TmsZoneMatchInfoVo> matchedZones = new ArrayList<>();

            for (TmsServiceQuoteEntity quote : quotes) {
                // 不可达分区
                if (quote.getUnreachableRegionId() != null) {
                    // 查询该报价规则下不可达分区明细
                    List<TmsServiceRegionDetailEntity> unRegionDetails = tmsServiceRegionDetailService.list(
                            new LambdaQueryWrapper<TmsServiceRegionDetailEntity>()
                                    .eq(TmsServiceRegionDetailEntity::getRegionId, quote.getUnreachableRegionId())
                                    .eq(TmsServiceRegionDetailEntity::getIsValid, 1)
                    );

                    // 检查邮政编码是否在不可达分区范围内，在则提示
                    for (TmsServiceRegionDetailEntity regionDetail : unRegionDetails) {
                        if (isPostalCodeInRange(shipperPostalCode, regionDetail.getPostalCode(), regionDetail.getPostalCode())) {
                            return new TmsZoneMatchResultVo(false, "该发货邮编存在于不可达分区范围内: " + shipperPostalCode);
                        }
                        if (isPostalCodeInRange(destPostalCodeParam, regionDetail.getPostalCode(), regionDetail.getPostalCode())) {
                            return new TmsZoneMatchResultVo(false, "该收货邮编存在于不可达分区范围内: " + destPostalCodeParam);
                        }
                    }

                }
                // 可达分区
                if (quote.getReachableRegionId() != null) {
                    // 查询该报价规则下可达分区明细
                    LambdaQueryWrapper<TmsServiceRegionDetailEntity> wrapper = new LambdaQueryWrapper<TmsServiceRegionDetailEntity>()
                            .eq(TmsServiceRegionDetailEntity::getRegionId, quote.getReachableRegionId())
                            .eq(TmsServiceRegionDetailEntity::getIsValid, 1)
                            .eq(TmsServiceRegionDetailEntity::getDelFlag, "0")
                            .likeRight(TmsServiceRegionDetailEntity::getShipperPostalCodeStart, StringUtils.left(shipperPostalCode, 3))  // 例如 V1M
                            .likeRight(TmsServiceRegionDetailEntity::getPostalCodeStart, StringUtils.left(destPostalCodeParam, 3));      // 例如 M3J
                    List<TmsServiceRegionDetailEntity> regionDetails = tmsServiceRegionDetailService.list(wrapper);

                    // 检查邮政编码是否在可达分区范围内（同时匹配发货地和收货地邮编）
                    for (TmsServiceRegionDetailEntity regionDetail : regionDetails) {
                        if (isPostalCodeInRange(shipperPostalCode, destPostalCodeParam, regionDetail)) {
                            //matchedZones.add(new TmsZoneMatchInfoVo(quote, regionDetail, regionDetail.getPostalName()));
                            log.debug("发货邮编 {} 和收货邮编 {} 匹配到纵区: {} (报价规则: {})", shipperPostalCode, destPostalCodeParam, regionDetail.getPostalName(), quote.getQuoteName());
                            return new TmsZoneMatchResultVo(quote, regionDetail.getPostalName());
                        }
                    }
                }
            }

            if (matchedZones.isEmpty()) {
                return new TmsZoneMatchResultVo(false,
                        "未找到匹配的服务商报价规则，邮编未匹配[" + shipperPostalCode + "，" + destPostalCodeParam+"]");
            }

            // ========== 多纵区冲突解决 ==========
            if (matchedZones.size() > 1) {
                // 使用跨城市判断逻辑确定最终纵区
                String crossCityResult = determineCrossCityType(shipperPostalCode, destPostalCodeParam, shipperCity, destCity);

                // 基于跨城市判断结果选择合适的纵区（精确匹配 zone name）
                TmsZoneMatchInfoVo selectedZone = null;
                if (StrUtil.isNotBlank(crossCityResult)) {
                    for (TmsZoneMatchInfoVo zone : matchedZones) {
                        if (crossCityResult.equals(zone.getPostalName())) {
                            selectedZone = zone;
                            break;
                        }
                    }
                }

                if (selectedZone != null) {
                    return new TmsZoneMatchResultVo(selectedZone.getQuote(), crossCityResult);
                } else {
                    // 没有精确找到符合 crossCityResult 的，就默认返回第一个（兜底逻辑）
                    return new TmsZoneMatchResultVo(matchedZones.get(0).getQuote(), crossCityResult);
                }
            }

            // ========== 单纵区直接返回 ==========
            TmsZoneMatchInfoVo singleZone = matchedZones.get(0);
            return new TmsZoneMatchResultVo(singleZone.getQuote(), singleZone.getPostalName());
        } catch (Exception e) {
            log.error("纵区判断失败，邮政编码: {}", shipperPostalCode+"-"+destPostalCodeParam, e);
            return new TmsZoneMatchResultVo(false, "纵区判断异常: " + e.getMessage());
        }
    }



    /**
     * 确定区域类型（Zone 1: 同城, Zone 2: 跨城市）
     * 邮编和城市名称的判断逻辑
     */
    private String determineCrossCityType(String shipperPostalCode, String destPostalCode, String shipperCity, String destCity) {
        try {
            // 通过邮编前缀判断
            String shipperPrefix = normalizePostalCode(shipperPostalCode);
            String destPrefix = normalizePostalCode(destPostalCode);

            if (StrUtil.isNotBlank(shipperPrefix) && StrUtil.isNotBlank(destPrefix)) {
                if (shipperPrefix.equals(destPrefix)) {
                    return "Zone 1"; // 同城
                } else {
                    return "Zone 2"; // 跨城市
                }
            }

            // 通过城市名称判断
/*            if (StrUtil.isNotBlank(shipperCity) && StrUtil.isNotBlank(destCity)) {
                String cleanShipperCity = extractCityFromAddress(shipperCity);
                String cleanDestCity = extractCityFromAddress(destCity);

                if (StrUtil.isNotBlank(cleanShipperCity) && StrUtil.isNotBlank(cleanDestCity)) {
                    if (cleanShipperCity.equalsIgnoreCase(cleanDestCity)) {
                        return "Zone 1"; // 同城
                    } else {
                        return "Zone 2"; // 跨城市
                    }
                }
            }*/
            // 默认为跨城市
            return "Zone 2";

        } catch (Exception e) {
            log.error("确定区域类型失败", e);
            return "Zone 2"; // 默认为跨城市
        }
    }

    /**
     * 从地址中提取城市名称
     * 支持多种格式：Canada/ON/Brampton、Toronto, ON、Calgary
     */
    private String extractCityFromAddress(String address) {
        if (StrUtil.isBlank(address)) {
            return null;
        }

        String trimmedAddress = address.trim();

        // 处理 Canada/ON/Brampton 格式（斜杠分隔）
        if (trimmedAddress.contains("/")) {
            String[] parts = trimmedAddress.split("/");
            if (parts.length >= 3) {
                return parts[parts.length - 1].trim(); // 取最后一部分作为城市名称
            } else if (parts.length == 2) {
                return parts[1].trim(); // 如果只有两部分，取第二部分
            }
        }

        // 处理 Toronto, ON 格式（逗号分隔）
        if (trimmedAddress.contains(",")) {
            String[] parts = trimmedAddress.split(",");
            if (parts.length > 0) {
                return parts[0].trim();
            }
        }

        // 如果没有分隔符，直接返回整个地址作为城市名称
        return trimmedAddress;
    }

    /**
     * 邮政编码范围检查 - 同时匹配发货地和收货地邮编是否在TmsServiceRegionDetailEntity的发件与目的地邮编区间范围内
     *
     * @param shipperPostalCode 发货地邮编
     * @param destPostalCode 收货地邮编
     * @param regionDetail 服务区域明细
     * @return 是否匹配
     */
    private boolean isPostalCodeInRange(String shipperPostalCode, String destPostalCode, TmsServiceRegionDetailEntity regionDetail) {
        // 检查发货地邮编是否在发件人邮编区间范围内
        boolean shipperMatch = isPostalCodeInRange(shipperPostalCode,
            regionDetail.getShipperPostalCodeStart(), regionDetail.getShipperPostalCodeEnd());
        // 检查收货地邮编是否在目的地邮编区间范围内
        boolean destMatch = isPostalCodeInRange(destPostalCode,
            regionDetail.getPostalCodeStart(), regionDetail.getPostalCodeEnd());
        // 两个邮编都必须匹配
        return shipperMatch && destMatch;
    }

    /**
     * 完整邮政编码范围检查 - 支持6位加拿大邮政编码范围匹配
     *
     * 示例：
     * - 起始邮编：V1M 0A0 -> V1M0A0
     * - 截止邮编：V1M 9Z9 -> V1M9Z9
     * - 订单邮编：V1M 2C9 -> V1M2C9
     * - 结果：true（V1M2C9在V1M0A0到V1M9Z9范围内）
     */
    private boolean isPostalCodeInRange(String postalCode, String startCode, String endCode) {
        if (StrUtil.isBlank(postalCode) || StrUtil.isBlank(startCode) || StrUtil.isBlank(endCode)) {
            return false;
        }

        try {
            // 使用完整的6位邮政编码格式化
            String normalizedPostalCode = normalizeFullPostalCode(postalCode);
            String normalizedStartCode = normalizeFullPostalCode(startCode);
            String normalizedEndCode = normalizeFullPostalCode(endCode);

            if (StrUtil.isBlank(normalizedPostalCode) || StrUtil.isBlank(normalizedStartCode) || StrUtil.isBlank(normalizedEndCode)) {
                return false;
            }

            // 加拿大邮政编码字典序比较（6位完整比较）
            boolean result = normalizedPostalCode.compareTo(normalizedStartCode) >= 0 &&
                           normalizedPostalCode.compareTo(normalizedEndCode) <= 0;
            return result;
        } catch (Exception e) {
            log.error("邮政编码范围比较失败: {} vs [{}, {}]", postalCode, startCode, endCode, e);
            return false;
        }
    }

    /**
     * 检查重量是否在指定范围内
     */
    private boolean isWeightInRange(BigDecimal weight, BigDecimal startWeight, BigDecimal endWeight) {
        if (weight == null || startWeight == null || endWeight == null) {
            return false;
        }
        return weight.compareTo(startWeight) >= 0 && weight.compareTo(endWeight) <= 0;
    }

    /**
     * 邮政编码前缀格式化方法（用于区域类型判断）
     * 去除空格和特殊字符，转为大写，取前3位
     */
    private String normalizePostalCodePrefix(String postalCode) {
        if (StrUtil.isBlank(postalCode)) {
            return null;
        }

        // 移除空格和特殊字符，只保留字母和数字
        String cleaned = postalCode.replaceAll("[^A-Za-z0-9]", "").toUpperCase();

        // 加拿大邮政编码格式：前3位字符
        if (cleaned.length() >= 3) {
            return cleaned.substring(0, 3);
        }

        return cleaned;
    }

    /**
     * 完整邮政编码格式化方法（用于范围匹配）
     * 去除空格和特殊字符，转为大写，保持完整的6位格式
     */
    private String normalizeFullPostalCode(String postalCode) {
        if (StrUtil.isBlank(postalCode)) {
            return null;
        }

        // 移除空格和特殊字符，只保留字母和数字
        String cleaned = postalCode.replaceAll("[^A-Za-z0-9]", "").toUpperCase();

        // 加拿大邮政编码格式：6位字符（A1A1A1）
        if (cleaned.length() >= 6) {
            return cleaned.substring(0, 6);
        } else if (cleaned.length() >= 3) {
            // 如果只有3位，补充为6位（如V1M -> V1M0A0）
            String prefix = cleaned.substring(0, 3);
            return prefix + "0A0";
        }

        return cleaned;
    }

    /**
     * 兼容性方法：保持原有的normalizePostalCode方法名
     * 默认使用前缀格式化（保持向后兼容）
     */
    private String normalizePostalCode(String postalCode) {
        return normalizePostalCodePrefix(postalCode);
    }


    /**
     * 根据客户的volumeWeightSwitch字段确定计费重量
     *
     * @param customerId 客户ID
     * @param actualWeight 实际重量
     * @param volumeWeight 体积重量
     * @return 计费重量
     */
    private BigDecimal calculateChargeWeight(Long customerId, BigDecimal actualWeight, BigDecimal volumeWeight) {
        try {
            // 查询客户的体积重开关设置
            TmsCustomerEntity customer = customerMapper.selectById(customerId);
            if (customer != null && customer.getVolumeWeightSwitch() != null && customer.getVolumeWeightSwitch()) {
                // 体积重开关开启：体积重与实际重取较大值
                BigDecimal actual = actualWeight != null ? actualWeight : BigDecimal.ZERO;
                BigDecimal volume = volumeWeight != null ? volumeWeight : BigDecimal.ZERO;
                return actual.max(volume);
            } else {
                // 体积重开关未开启：直接使用实际重作为计费重
                return actualWeight != null ? actualWeight : BigDecimal.ZERO;
            }
        } catch (Exception e) {
            log.error("计算计费重量失败，客户ID: {}", customerId, e);
            // 异常情况下使用原逻辑：体积重与实际重取较大值
            BigDecimal actual = actualWeight != null ? actualWeight : BigDecimal.ZERO;
            BigDecimal volume = volumeWeight != null ? volumeWeight : BigDecimal.ZERO;
            return actual.max(volume);
        }
    }

    /**
     * 获取超重订单的计费系数
     *
     * @param zoneType 区域类型
     * @return 计费系数
     */
    private BigDecimal getOverweightCoefficient(String zoneType) {
        if ("Zone 1".equals(zoneType)) {
            return new BigDecimal("0.4");
        } else if ("Zone 2".equals(zoneType)) {
            return new BigDecimal("1.9");
        } else {
            // 默认使用0.4
            return new BigDecimal("0.4");
        }
    }

    /**
     * 计算最终价格：基础价格=（基础价格 × 客户等级系数）； 附加费暂未计算
     *
     * @param basePrice 基础价格
     * @param profitRate 利润率
     * @param customerId 客户ID，用于查询客户等级
     * @return 最终价格
     */
    private BigDecimal calculateFinalPrice(BigDecimal basePrice, BigDecimal profitRate, Long customerId) {
        if (basePrice == null) {
            return BigDecimal.ZERO;
        }

        // 根据客户等级系数调整基础价格
        BigDecimal customerLevelCoefficient = getCustomerLevelCoefficient(customerId);
        BigDecimal adjustedBasePrice = basePrice.multiply(customerLevelCoefficient);
        log.debug("客户ID: {}, 等级系数: {}, 原始基础价格: {}, 调整后基础价格: {}", customerId, customerLevelCoefficient, basePrice, adjustedBasePrice);

        // 基于调整后基础价格计算利润
        if (profitRate == null || profitRate.compareTo(BigDecimal.ZERO) == 0) {
            return adjustedBasePrice.setScale(2, RoundingMode.HALF_UP);
        }

        // 计算利润金额：调整后基础价格 × (利润率 / 100)
//        BigDecimal profitAmount = adjustedBasePrice.multiply(profitRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));

        // 调整后基础价格 + 利润 = 最终价格
        //BigDecimal finalPrice = adjustedBasePrice.add(profitAmount);
        BigDecimal finalPrice = adjustedBasePrice;

        // 保留2位小数
        return finalPrice.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 从destination字段中提取省份信息
     * destination数据格式示例：Canada/ON/Toronto
     *
     * @param destination 目的地字符串
     * @return 省份代码，如果解析失败返回null
     */
    private String extractProvinceFromDestination(String destination) {
        if (StrUtil.isBlank(destination)) {
            return null;
        }
        try {
            String[] parts = destination.split("/");
            if (parts.length >= 2) {
                // 返回第二部分作为省份代码
                return parts[1].trim();
            }
        } catch (Exception e) {
            log.warn("解析destination失败: {}", destination, e);
        }
        return null;
    }

    /**
     * 根据省份查询税率并计算总税率
     *
     * @param province 省份代码
     * @return 总税率（GST + PST + HST + QST），如果查询失败返回0
     */
    private BigDecimal getTaxRateByProvince(String province) {
        if (StrUtil.isBlank(province)) {
            return BigDecimal.ZERO;
        }

        try {
            LambdaQueryWrapper<TmsTaxRateEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TmsTaxRateEntity::getProvince, province)
                   .eq(TmsTaxRateEntity::getIsValid, 1)
                   .last("LIMIT 1");

            TmsTaxRateEntity taxRateEntity = tmsTaxRateService.getOne(wrapper,false);
            if (taxRateEntity == null) {
                log.debug("未找到省份 {} 的税率配置", province);
                return BigDecimal.ZERO;
            }

            // 计算总税率：GST + PST + HST + QST
            BigDecimal totalTaxRate = BigDecimal.ZERO;
            if (taxRateEntity.getGst() != null && taxRateEntity.getGst().compareTo(BigDecimal.ZERO) > 0) {
                totalTaxRate = totalTaxRate.add(taxRateEntity.getGst());
            }
            if (taxRateEntity.getPst() != null && taxRateEntity.getPst().compareTo(BigDecimal.ZERO) > 0) {
                totalTaxRate = totalTaxRate.add(taxRateEntity.getPst());
            }
            if (taxRateEntity.getHst() != null && taxRateEntity.getHst().compareTo(BigDecimal.ZERO) > 0) {
                totalTaxRate = totalTaxRate.add(taxRateEntity.getHst());
            }
            if (taxRateEntity.getQst() != null && taxRateEntity.getQst().compareTo(BigDecimal.ZERO) > 0) {
                totalTaxRate = totalTaxRate.add(taxRateEntity.getQst());
            }

            log.debug("省份 {} 的总税率: {}%", province, totalTaxRate);
            return totalTaxRate;

        } catch (Exception e) {
            log.error("查询省份 {} 税率失败", province, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 根据客户ID获取客户等级系数
     *
     * @param customerId 客户ID
     * @return 客户等级系数
     */
    private BigDecimal getCustomerLevelCoefficient(Long customerId) {
        try {
            // 查询客户信息获取客户等级
            TmsCustomerEntity customer = customerMapper.selectById(customerId);
            if (customer != null && customer.getCustomerLevel() != null) {
                Integer customerLevel = customer.getCustomerLevel();
                switch (customerLevel) {
                    case 1:
                        // 1级客户：基础价格 × 1.0
                        return new BigDecimal("1.0");
                    case 2:
                        // 2级客户：基础价格 × 1.2
                        return new BigDecimal("1.2");
                    case 3:
                        // 3级客户：基础价格 × 1.5
                        return new BigDecimal("1.5");
                    default:
                        // 未知等级默认按3级客户处理
                        log.warn("客户ID: {} 的等级 {} 不在预期范围内，默认按3级客户处理", customerId, customerLevel);
                        return new BigDecimal("1.5");
                }
            } else {
                // 无法获取客户等级信息，默认按3级客户处理
                log.warn("无法获取客户ID: {} 的等级信息，默认按3级客户处理", customerId);
                return new BigDecimal("1.5");
            }
        } catch (Exception e) {
            // 异常情况下默认按3级客户处理
            log.error("查询客户等级失败，客户ID: {}，默认按3级客户处理", customerId, e);
            return new BigDecimal("1.5");
        }
    }

    /**
     * 价格计算并创建应收记录API - 基于主单数据计算价格并生成应收记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PriceCalculationAndReceivableResultVo calculatePriceAndCreateReceivable(PriceCalculationYingShouRequestVo request) {
        PriceCalculationAndReceivableResultVo result = new PriceCalculationAndReceivableResultVo();
        result.setCompletionTime(LocalDateTime.now());

        try {
            log.info("开始价格计算并创建应收记录，主单数量: {}", request.getOrders().size());

            // ========== 参数验证 ==========
            if (CollUtil.isEmpty(request.getOrders())) {
                result.setSuccess(false);
                result.setErrorMessage("主单列表不能为空");
                return result;
            }

            // 判断传入参数是否有重复
            List<String> distinctOrderNumbers = request.getOrders().stream()
                .map(MasterOrderPriceCalculationVo::getOrderNumber)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

            // ==========重复检查 ==========  检查传入主单是否已经存在应收记录(跳过这些重复的主单号，继续处理其他订单)
            List<String> duplicateOrderNos = checkDuplicateMasterOrderReceivableRecords(distinctOrderNumbers);
            // 如果输入订单的数量和重复订单的数量相等，则直接返回错误
            if (distinctOrderNumbers.size() == duplicateOrderNos.size()) {
                result.setSuccess(false);
                result.setErrorMessage("所有主单订单号已存在应收记录，无法重复创建: " + String.join(", ", duplicateOrderNos));
                log.warn("所有主单订单号已存在应收记录，无法重复创建: {}", String.join(", ", duplicateOrderNos));
                return result; // 直接返回，不继续执行后续计算
            }

            if (!duplicateOrderNos.isEmpty()) {
                result.setErrorMessage("发现重复的主单应收记录，无法重复创建: " + String.join(", ", duplicateOrderNos));
                log.warn("发现重复的应收记录，拒绝创建: {}", String.join(", ", duplicateOrderNos));
            }

            // ========== 第2步：根据主单号查询订单信息 ==========
            List<TmsCustomerOrderEntity> orderList = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, distinctOrderNumbers)
                    .eq(TmsCustomerOrderEntity::getSubFlag, false) // 查询主单
                    .eq(TmsCustomerOrderEntity::getDelFlag, "0"));

            if (CollUtil.isEmpty(orderList)) {
                result.setSuccess(false);
                result.setErrorMessage("没有找到相关的主单信息");
                return result;
            }

            // ========== 构建价格计算请求，使用传入的主单汇总数据 ==========
            PriceCalculationRequestVo priceRequest = new PriceCalculationRequestVo();
            List<TmsOrderPriceCalculationVo> priceOrders = new ArrayList<>();

            // 创建主单号到汇总数据的映射
            Map<String, MasterOrderPriceCalculationVo> masterOrderMap = request.getOrders().stream()
                .collect(Collectors.toMap(MasterOrderPriceCalculationVo::getOrderNumber, Function.identity()));

            for (TmsCustomerOrderEntity order : orderList) {
                if (duplicateOrderNos.contains(order.getEntrustedOrderNumber())) {
                    log.info("跳过重复订单: {}", order.getEntrustedOrderNumber());
                    continue; // 跳过重复的订单
                }

                MasterOrderPriceCalculationVo masterOrder = masterOrderMap.get(order.getEntrustedOrderNumber());
                if (masterOrder != null) {
                    TmsOrderPriceCalculationVo vo = new TmsOrderPriceCalculationVo();
                    BeanUtils.copyProperties(order, vo);
                    // 使用传入的汇总数据
                    vo.setVolumeWeight(masterOrder.getVolumeWeight());
                    vo.setActualWeight(masterOrder.getActualWeight());
                    vo.setScanTime(masterOrder.getScanTime());
                    priceOrders.add(vo);
                }
            }

            priceRequest.setOrders(priceOrders);
            priceRequest.setProviderName(request.getProviderName());
            priceRequest.setSourceChannel(request.getSourceChannel());

            // ========== 价格计算 ==========
            PriceCalculationResultVo priceResult = calculatePrice(priceRequest);
            result.setPriceCalculationResult(priceResult);

            // 检查是否有成功计算的主单
            if (priceResult.getSuccessCount() == 0) {
                result.setSuccess(false);
                result.setErrorMessage("没有成功计算价格的主单，无法创建应收记录");
                return result;
            }

            // ========== 创建主单维度的应收记录 ==========
            PriceCalculationAndReceivableResultVo.ReceivableCreationResultVo creationResult = createMasterOrderReceivableRecords(priceResult);
            result.setReceivableCreationResult(creationResult);
            result.setSuccess(true);
        } catch (Exception e) {
            log.error("价格计算并创建应收记录失败", e);
            result.setSuccess(false);
            result.setErrorMessage("操作失败: " + e.getMessage());
            throw e; // 重新抛出异常以触发事务回滚
        }

        return result;
    }

    @Override
    public TmsReceivableEntity getReceivableByEntrustedOrderNo(String entrustedOrderNo) {
        if(StrUtil.isBlank(entrustedOrderNo)){
            return null;
        }
        LambdaQueryWrapper<TmsReceivableEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsReceivableEntity::getEntrustedOrderNo, entrustedOrderNo);
        queryWrapper.last("limit 1");
        return baseMapper.selectOne(queryWrapper);
    }

    /**
     * 防重复检查 - 检查主单是否已存在应收记录
     *
     * @param orderNumbers 主单号列表
     * @return 如果存在重复记录返回错误信息，否则返回null
     */
    private List<String> checkDuplicateMasterOrderReceivableRecords(List<String> orderNumbers) {
        if (orderNumbers == null || orderNumbers.isEmpty()) {
            return Collections.emptyList();
        }

        // 查询是否已存在主单应收记录
        List<TmsReceivableEntity> existingRecords = tmsReceivableMapper.selectList(
                new LambdaQueryWrapper<TmsReceivableEntity>()
                        .in(TmsReceivableEntity::getEntrustedOrderNo, orderNumbers)
                        .eq(TmsReceivableEntity::getSubFlag, 0)); // 只查询主单记录

        if (CollUtil.isNotEmpty(existingRecords)) {
            return existingRecords.stream()
                    .map(TmsReceivableEntity::getEntrustedOrderNo)
                    .collect(Collectors.toList());
        }

        return Collections.emptyList();
    }


    /**
     * 创建主单维度的应收记录
     */
    private PriceCalculationAndReceivableResultVo.ReceivableCreationResultVo createMasterOrderReceivableRecords(PriceCalculationResultVo priceResult) {
        //应收记录创建结果记录
        PriceCalculationAndReceivableResultVo.ReceivableCreationResultVo creationResult = new PriceCalculationAndReceivableResultVo.ReceivableCreationResultVo();
        creationResult.setCreationTime(LocalDateTime.now());

        // 过滤出成功计算的主单
        List<PriceCalculationDetailVo> successDetails = priceResult.getDetails().stream()
            .filter(PriceCalculationDetailVo::getSuccess)
            .collect(Collectors.toList());

        if (successDetails.isEmpty()) {
            throw new RuntimeException("没有成功计算价格的主单");
        }

        log.info("开始创建主单维度应收记录，共 {} 个主单", successDetails.size());

        // ========== 直接处理所有主单 ==========
        List<String> allMasterReceivableNos = new ArrayList<>();
        int totalCreatedCount = 0;

        for (PriceCalculationDetailVo detail : successDetails) {
            String masterReceivableNo = detail.getEntrustedOrderNumber();
            // 创建主单应收记录
            TmsReceivableEntity masterReceivable = createMasterReceivableEntity(detail);
            // 插入主单记录
            tmsReceivableMapper.insert(masterReceivable);
            allMasterReceivableNos.add(masterReceivableNo);
            totalCreatedCount++;
        }

        // ========== 设置返回结果 ==========
        creationResult.setSubReceivableNos(allMasterReceivableNos);
        creationResult.setTotalCreatedCount(totalCreatedCount);

        log.info("主单维度应收记录创建完成，共创建 {} 条主单记录", totalCreatedCount);
        return creationResult;
    }


    /**
     * 创建主单实体
     * 从PriceCalculationDetailVo映射到TmsReceivableEntity，用于主单维度的应收记录
     */
    private TmsReceivableEntity createMasterReceivableEntity(PriceCalculationDetailVo detail) {
        String entrustedOrderNumber = detail.getEntrustedOrderNumber();

        //统计主单件数
        Long orderCount = customerOrderMapper.selectCount(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getSubFlag,true)
                .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber));

        // 使用传入的汇总数据
        BigDecimal actualWeight = detail.getActualWeight();     // 实际重量（已汇总）
        BigDecimal volumeWeight = detail.getVolumeWeight();     // 体积重（已汇总）
        BigDecimal totalVolume = detail.getVolume();            // 体积（已汇总）
        Integer totalPackageCount = Math.toIntExact(orderCount == null || orderCount == 0 ? 1 : orderCount);  // 主单件数默认为1
        Long customerId = detail.getCustomerId();              // 客户id

        TmsReceivableEntity entity = new TmsReceivableEntity();

        // ========== 基本信息 ==========
        entity.setSubFlag(0); // 标识为主单
        entity.setEntrustedOrderNo(detail.getEntrustedOrderNumber());
        entity.setCustomerOrderNo(detail.getCustomerOrderNumber());

        // ========== 重量信息 ==========
        entity.setActualWeight(actualWeight); // 实际重量（已汇总）
        entity.setChargeWeight(detail.getFinalWeightForMatching()); // 计费重量
        entity.setForecastedWeight(detail.getOrderWeight()); // 预报重量
        entity.setVolumeWeight(volumeWeight); // 体积重（已汇总）

        // ========== 体积和件数信息 ==========
        entity.setVolume(totalVolume); // 体积（已汇总）
        entity.setPackageCount(totalPackageCount); // 主单件数

        // ========== 价格信息 ==========
        entity.setBaseFreight(detail.getBasePrice()); // 基础运费
        entity.setTaxFee(detail.getTaxFee()); // 税费
        entity.setTotalAmount(detail.getFinalPrice()); // 应收总额
        entity.setSurcharge(detail.getAdditionalFee()); // 附加费
        entity.setSurchargeDetail(detail.getSurchargeDetail() != null ? detail.getSurchargeDetail() : ""); // 附加费明细

        // ========== 其他信息 ==========
        entity.setOrderTime(detail.getScanTime()); // 扫描时间
        entity.setCustomerId(customerId);

        return entity;
    }

    /**
     * 计算单个附加费规则的费用
     *
     * @param orderContext 订单数据上下文
     * @param feeRule 附加费规则
     * @param basePrice 基础运费
     * @return 该规则产生的费用
     */
    private BigDecimal calculateSingleFeeRuleOptimized(OrderDataContext orderContext, TmsFeeRuleEntity feeRule, BigDecimal basePrice) {
        Integer feeType = feeRule.getFeeType();
        Integer calculationMethod = feeRule.getCalculationMethod();
        Integer unit = feeRule.getUnit();

        // 对于按件计费的表达式类型附加费（操作附加费、超限附加费、超大包裹附加费），需要按子单逐一检查
        if (unit == 2 && (feeType == 2 || feeType == 3 || feeType == 4)) {
            return calculateSubOrderBasedFee(orderContext, feeRule, basePrice);
        }

         // 判断其余附加费条件是否触发
        if (!checkFeeRuleTriggerConditionOptimized(orderContext, feeRule)) {
            return BigDecimal.ZERO;
        }

        // 根据计算方式计算费用
        switch (calculationMethod) {
            case 1: // 固定收费
                return calculateFixedFeeOptimized(feeRule, unit, orderContext);
            case 3: // 百分比收费
                return calculatePercentageFeeOptimized(feeRule, basePrice, unit, orderContext);
            default:
                log.warn("未知的计算方式: {}", calculationMethod);
                return BigDecimal.ZERO;
        }
    }

    /**
     * 按子单计算附加费（用于操作附加费、超限附加费、超大包裹附加费）
     *
     * @param orderContext 订单数据上下文
     * @param feeRule 附加费规则
     * @param basePrice 基础运费
     * @return 附加费总额
     */
    private BigDecimal calculateSubOrderBasedFee(OrderDataContext orderContext, TmsFeeRuleEntity feeRule, BigDecimal basePrice) {
        try {
            List<String> subOrderNumbers = orderContext.getSubOrderNumbers();
            Map<String, TmsSortingRecordEntity> subOrderSortingRecords = orderContext.getSubOrderSortingRecords();
            TmsOrderPriceCalculationVo orderVo = orderContext.getOrderVo();

            BigDecimal totalFee = BigDecimal.ZERO;
            int qualifiedSubOrderCount = 0;

            // 查询表达式
            LambdaQueryWrapper<TmsFeeRuleExprEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TmsFeeRuleExprEntity::getFeeId, feeRule.getId());
            TmsFeeRuleExprEntity exprEntity = tmsFeeRuleExprMapper.selectOne(wrapper,false);

            if (exprEntity == null || StrUtil.isBlank(exprEntity.getExpression())) {
                log.warn("附加费规则 {} 未配置表达式", feeRule.getId());
                return BigDecimal.ZERO;
            }

            // 判断是分拣场景还是询价场景
            if (CollUtil.isNotEmpty(subOrderNumbers)) {
                // 分拣场景：使用子单号和分拣记录
                qualifiedSubOrderCount = calculateSubOrderBasedFeeForSorting(subOrderNumbers, subOrderSortingRecords, orderContext, exprEntity, feeRule);
            } else if (CollUtil.isNotEmpty(orderVo.getSubOrderInfos())) {
                // 询价场景：使用预报子单信息列表
                qualifiedSubOrderCount = calculateSubOrderBasedFeeForInquiry(orderVo.getSubOrderInfos(), exprEntity, feeRule);
            } else {
                log.debug("订单 {} 没有子单信息，跳过按子单计算附加费", orderVo.getCustomerOrderNumber());
                return BigDecimal.ZERO;
            }

            // 计算总费用：满足条件的子单数量 × 单价
            if (qualifiedSubOrderCount > 0) {
                BigDecimal amount = feeRule.getAmount();
                if (amount != null) {
                    totalFee = amount.multiply(new BigDecimal(qualifiedSubOrderCount));
                    log.debug("附加费规则 {} 计算结果：满足条件子单数 {} × 单价 {} = 总费用 {}", feeRule.getFeeName(), qualifiedSubOrderCount, amount, totalFee);
                }
            }

            return totalFee;
        } catch (Exception e) {
            log.error("按子单计算附加费异常，规则: {}", feeRule.getFeeName(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 分拣场景下按子单计算附加费
     *
     * @param subOrderNumbers 子单号列表
     * @param subOrderSortingRecords 子单分拣记录映射
     * @param orderContext 订单数据上下文
     * @param exprEntity 表达式实体
     * @param feeRule 附加费规则
     * @return 满足条件的子单数量
     */
    private int calculateSubOrderBasedFeeForSorting(List<String> subOrderNumbers,
                                                   Map<String, TmsSortingRecordEntity> subOrderSortingRecords,
                                                   OrderDataContext orderContext,
                                                   TmsFeeRuleExprEntity exprEntity,
                                                   TmsFeeRuleEntity feeRule) {
        int qualifiedSubOrderCount = 0;

        for (String subOrderNumber : subOrderNumbers) {
            TmsSortingRecordEntity sortingRecord = subOrderSortingRecords.get(subOrderNumber);
            if (sortingRecord == null) {
                log.debug("子单 {} 没有分拣记录，跳过", subOrderNumber);
                continue;
            }

            // 构建该子单的变量映射
            Map<String, Object> subOrderVariables = buildSubOrderVariables(orderContext, sortingRecord);

            // 检查该子单是否满足表达式条件
            try {
                boolean isQualified = SpelExpressionEvaluator.evaluate(exprEntity.getExpression(), subOrderVariables);
                if (isQualified) {
                    qualifiedSubOrderCount++;
                    log.debug("子单 {} 满足附加费条件: {}", subOrderNumber, feeRule.getFeeName());
                }
            } catch (Exception e) {
                log.error("子单 {} 表达式计算异常，表达式: {}", subOrderNumber, exprEntity.getExpression(), e);
            }
        }

        return qualifiedSubOrderCount;
    }

    /**
     * 询价场景下按子单计算附加费
     *
     * @param subOrderInfos 子单信息列表
     * @param exprEntity 表达式实体
     * @param feeRule 附加费规则
     * @return 满足条件的子单数量
     */
    private int calculateSubOrderBasedFeeForInquiry(List<TmsSubOrderInfoVo> subOrderInfos, TmsFeeRuleExprEntity exprEntity, TmsFeeRuleEntity feeRule) {
        int qualifiedSubOrderCount = 0;

        for (int i = 0; i < subOrderInfos.size(); i++) {
            TmsSubOrderInfoVo subOrderInfo = subOrderInfos.get(i);

            // 根据长宽高计算出体积
            if (subOrderInfo.getLength() != null && subOrderInfo.getWidth() != null && subOrderInfo.getHeight() != null) {
                BigDecimal volume = subOrderInfo.getLength().multiply(subOrderInfo.getWidth()).multiply(subOrderInfo.getHeight())
                        .divide(new BigDecimal("1000000"), 6, RoundingMode.HALF_UP); // 转换为 m³
                subOrderInfo.setVolume(volume);
            }

            // 构建该子单的变量映射
            Map<String, Object> subOrderVariables = buildSubOrderVariablesFromInfo(subOrderInfo);

            // 检查该子单是否满足表达式条件
            try {
                boolean isQualified = SpelExpressionEvaluator.evaluate(exprEntity.getExpression(), subOrderVariables);
                if (isQualified) {
                    qualifiedSubOrderCount++;
                    log.debug("询价子单 {} 满足附加费条件: {}", i + 1, feeRule.getFeeName());
                }
            } catch (Exception e) {
                log.error("询价子单 {} 表达式计算异常，表达式: {}", i + 1, exprEntity.getExpression(), e);
            }
        }

        return qualifiedSubOrderCount;
    }

    /**
     * 从子单信息中构建 SpEL 表达式所需的变量映射（null 值转 BigDecimal.ZERO）
     */
    private Map<String, Object> buildSubOrderVariablesFromInfo(TmsSubOrderInfoVo subOrderInfo) {
        Map<String, Object> variables = new HashMap<>();

        // 长宽高重量体积：null 时替换为 BigDecimal.ZERO
        variables.put("length", defaultBigDecimal(subOrderInfo.getLength()));
        variables.put("width", defaultBigDecimal(subOrderInfo.getWidth()));
        variables.put("height", defaultBigDecimal(subOrderInfo.getHeight()));
        variables.put("weight", defaultBigDecimal(subOrderInfo.getWeight()));
        variables.put("volume", defaultBigDecimal(subOrderInfo.getVolume()));

        // 计算 flangeLength（长、宽、高中最大值，全部 null 则为 0）
        List<BigDecimal> dimensions = new ArrayList<>();
        dimensions.add(defaultBigDecimal(subOrderInfo.getLength()));
        dimensions.add(defaultBigDecimal(subOrderInfo.getWidth()));
        dimensions.add(defaultBigDecimal(subOrderInfo.getHeight()));

        BigDecimal flangeLength = dimensions.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
        variables.put("flangeLength", flangeLength);

        log.debug("询价子单变量映射: {}", variables);
        return variables;
    }


    /**
     * 从分拣记录中构建 SpEL 表达式所需的变量映射（null 转 BigDecimal.ZERO）
     */
    private Map<String, Object> buildSubOrderVariables(OrderDataContext orderContext, TmsSortingRecordEntity sortingRecord) {
        Map<String, Object> variables = new HashMap<>();

        // 长宽高重量体积：null 时替换为 BigDecimal.ZERO
        variables.put("length", defaultBigDecimal(sortingRecord.getPackageLength()));
        variables.put("width", defaultBigDecimal(sortingRecord.getPackageWidth()));
        variables.put("height", defaultBigDecimal(sortingRecord.getPackageHeight()));
        variables.put("weight", defaultBigDecimal(sortingRecord.getPackageWeight()));
        variables.put("volume", defaultBigDecimal(sortingRecord.getPackageVolume()));

        // 计算 flangeLength（长、宽、高中最大值，全部 null 则为 0）
        List<BigDecimal> dimensions = new ArrayList<>();
        dimensions.add(defaultBigDecimal(sortingRecord.getPackageLength()));
        dimensions.add(defaultBigDecimal(sortingRecord.getPackageWidth()));
        dimensions.add(defaultBigDecimal(sortingRecord.getPackageHeight()));

        BigDecimal flangeLength = dimensions.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
        variables.put("flangeLength", flangeLength);

        return variables;
    }

    /**
     * 工具方法：null 转 BigDecimal.ZERO
     */
    private BigDecimal defaultBigDecimal(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }

    /**
     * 检查附加费规则的触发条件
     */
    private boolean checkFeeRuleTriggerConditionOptimized(OrderDataContext orderContext, TmsFeeRuleEntity feeRule) {
        Integer feeType = feeRule.getFeeType();

        switch (feeType) {
            case 5: // 危险品附加费
                return checkDangerousGoodsConditionOptimized(orderContext);
            case 6: // 地址更改附加费
                return false; // 暂时留空，待后续补充
            case 7: // 退件附加费 checkReturnConditionOptimized(orderContext);
                return false; // 暂时留空，待后续补充
            case 8: // 自提附加费
                return false; // 暂时留空，待后续补充
            case 9: // POD签名费
                return false; // 暂时留空，待后续补充
            default:
                log.warn("未知的附加费类型: {}", feeType);
                return false;
        }
    }


    /**
     * 检查危险品条件
     */
    private boolean checkDangerousGoodsConditionOptimized(OrderDataContext orderContext) {
        try {
            TmsCustomerOrderEntity orderEntity = orderContext.getOrderEntity();
            if (orderEntity != null && orderEntity.getCargoType() != null) {
                // 货物类型为2表示危险货物
                return orderEntity.getCargoType() == 2;
            }

            // 如果订单实体中没有货物类型（询价场景），则从orderVo中获取
            TmsOrderPriceCalculationVo orderVo = orderContext.getOrderVo();
            if (orderVo != null && orderVo.getCargoType() != null) {
                return orderVo.getCargoType() == 2;
            }

            return false;
        } catch (Exception e) {
            log.error("危险品条件检查异常", e);
            return false;
        }
    }

    /**
     * 检查退件条件
     */
    private boolean checkReturnConditionOptimized(OrderDataContext orderContext) {
        try {
            TmsCustomerOrderEntity orderEntity = orderContext.getOrderEntity();
            if (orderEntity != null && orderEntity.getDeliveryTry() != null) {
                // 派送尝试次数大于2表示三次配送失败
                return orderEntity.getDeliveryTry() > 2;
            }

            log.debug("订单 {} 未找到派送尝试次数信息，跳过退件附加费", orderContext.getOrderVo().getCustomerOrderNumber());
            return false;
        } catch (Exception e) {
            log.error("退件条件检查异常", e);
            return false;
        }
    }



    /**
     * 计算固定收费
     */
    private BigDecimal calculateFixedFeeOptimized(TmsFeeRuleEntity feeRule, Integer unit, OrderDataContext orderContext) {
        BigDecimal amount = feeRule.getAmount();
        if (amount == null) {
            return BigDecimal.ZERO;
        }

        // 按票计费：固定费用 × 1票
        if (unit == 1) {
            return amount;
        }
        // 按件计费：费用 × 件数
        else if (unit == 2) {
            int pieceCount = getPieceCount(orderContext);
            return amount.multiply(new BigDecimal(pieceCount));
        }

        return BigDecimal.ZERO;
    }


    /**
     * 计算百分比收费
     */
    private BigDecimal calculatePercentageFeeOptimized(TmsFeeRuleEntity feeRule, BigDecimal basePrice, Integer unit, OrderDataContext orderContext) {
        BigDecimal deliveryPercentage = feeRule.getDeliveryPercentage();
        if (deliveryPercentage == null || basePrice == null) {
            return BigDecimal.ZERO;
        }

        // 将百分比转换为小数
        BigDecimal percentageDecimal = deliveryPercentage.divide(new BigDecimal("100"), 10, RoundingMode.HALF_UP);

        // 按票计费：基础运费 × 百分比
        if (unit == 1) {
            return basePrice.multiply(percentageDecimal).setScale(2, RoundingMode.HALF_UP);
        }
        // 按件计费：(基础运费 × 百分比) × 件数
        else if (unit == 2) {
            int pieceCount = getPieceCount(orderContext);
            BigDecimal feePerPiece = basePrice.multiply(percentageDecimal);
            return feePerPiece.multiply(new BigDecimal(pieceCount)).setScale(2, RoundingMode.HALF_UP);
        }

        return BigDecimal.ZERO;
    }

    /**
     * 获取订单件数
     *
     * @param orderContext 订单数据上下文
     * @return 件数
     */
    private int getPieceCount(OrderDataContext orderContext) {
        try {
            // 从订单实体中获取件数
            TmsCustomerOrderEntity orderEntity = orderContext.getOrderEntity();
            if (orderEntity != null && orderEntity.getCargoQuantity() != null && orderEntity.getCargoQuantity() > 0) {
                return orderEntity.getCargoQuantity();
            }
            return 1;
        } catch (Exception e) {
            log.error("获取订单件数异常", e);
            return 1;
        }
    }

    /**
     * 构建订单数据上下文
     *
     * @param orderVo 订单价格计算VO
     * @param sourceChannel 来源渠道：1-询价，2-分拣计算价格
     * @return 订单数据上下文
     */
    private OrderDataContext buildOrderDataContext(TmsOrderPriceCalculationVo orderVo, Integer sourceChannel) {
        try {
            OrderDataContext context = new OrderDataContext(orderVo);

            if (sourceChannel != null && sourceChannel == 1) {
                // 询价场景：使用请求参数构建数据上下文
                return buildInquiryOrderDataContext(orderVo, context);
            } else {
                // 分拣计算价格场景：使用数据库查询构建数据上下文
                return buildSortingOrderDataContext(orderVo, context);
            }
        } catch (Exception e) {
            log.error("构建订单数据上下文失败，订单号: {}, 来源渠道: {}", orderVo.getCustomerOrderNumber(), sourceChannel, e);
            return null;
        }
    }

    /**
     * 构建询价场景下的订单数据上下文
     *
     * @param orderVo 订单价格计算VO
     * @param context 基础上下文
     * @return 订单数据上下文
     */
    private OrderDataContext buildInquiryOrderDataContext(TmsOrderPriceCalculationVo orderVo, OrderDataContext context) {
        // 询价场景下，创建虚拟的订单实体用于货物类型判断
        TmsCustomerOrderEntity virtualOrderEntity = new TmsCustomerOrderEntity();
        virtualOrderEntity.setCargoType(orderVo.getCargoType());
        context.setOrderEntity(virtualOrderEntity);

        // 询价场景下不需要设置子单号列表和分拣记录，因为询价时还没有实际的子单号
        // 子单信息将直接从orderVo.getSubOrderInfos()获取
        return context;
    }

    /**
     * 构建分拣计算价格场景下的订单数据上下文
     *
     * @param orderVo 订单价格计算VO
     * @param context 基础上下文
     * @return 订单数据上下文
     */
    private OrderDataContext buildSortingOrderDataContext(TmsOrderPriceCalculationVo orderVo, OrderDataContext context) {
        // 查询完整的订单信息
        LambdaQueryWrapper<TmsCustomerOrderEntity> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderVo.getEntrustedOrderNumber());
        TmsCustomerOrderEntity orderEntity = customerOrderMapper.selectOne(orderWrapper);
        context.setOrderEntity(orderEntity);

        // 获取子单号列表和对应的分拣记录
        List<String> subOrderNumbers = getSubOrderNumbers(orderVo.getEntrustedOrderNumber());
        Map<String, TmsSortingRecordEntity> subOrderSortingRecords = getSubOrderSortingRecords(subOrderNumbers);
        context.setSubOrderNumbers(subOrderNumbers);
        context.setSubOrderSortingRecords(subOrderSortingRecords);

        log.debug("构建分拣订单数据上下文完成，订单号: {}, 子单数量: {}", orderVo.getCustomerOrderNumber(), subOrderNumbers.size());
        return context;
    }

    /**
     * 获取子单号列表
     *
     * @param mainOrderNumber 主单号
     * @return 子单号列表
     */
    private List<String> getSubOrderNumbers(String mainOrderNumber) {
        try {
            // 查询所有子单
            LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNumber)
                   .eq(TmsCustomerOrderEntity::getSubFlag, true)
                   .orderByAsc(TmsCustomerOrderEntity::getEntrustedOrderNumber);

            List<TmsCustomerOrderEntity> subOrders = customerOrderMapper.selectList(wrapper);

            if (CollUtil.isEmpty(subOrders)) {
                // 如果没有子单，返回主单号
                return Arrays.asList(mainOrderNumber);
            }

            return subOrders.stream()
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取子单号列表失败，主单号: {}", mainOrderNumber, e);
            return Arrays.asList(mainOrderNumber);
        }
    }

    /**
     * 获取子单号对应的分拣记录
     *
     * @param subOrderNumbers 子单号列表
     * @return 子单号对应的最新分拣记录映射
     */
    private Map<String, TmsSortingRecordEntity> getSubOrderSortingRecords(List<String> subOrderNumbers) {
        Map<String, TmsSortingRecordEntity> result = new HashMap<>();

        if (CollUtil.isEmpty(subOrderNumbers)) {
            return result;
        }

        try {
            for (String subOrderNumber : subOrderNumbers) {
                // 为每个子单号获取最新的分拣记录
                LambdaQueryWrapper<TmsSortingRecordEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(TmsSortingRecordEntity::getOrderNo, subOrderNumber)
                       .orderByDesc(TmsSortingRecordEntity::getCreateTime)
                       .last("LIMIT 1");

                TmsSortingRecordEntity sortingRecord = tmsSortingRecordService.getOne(wrapper);
                if (sortingRecord != null) {
                    result.put(subOrderNumber, sortingRecord);
                    log.debug("获取子单 {} 的分拣记录成功", subOrderNumber);
                } else {
                    log.debug("子单 {} 未找到分拣记录", subOrderNumber);
                }
            }
        } catch (Exception e) {
            log.error("获取子单分拣记录失败", e);
        }

        return result;
    }

    /**
     * 订单数据上下文（用于附加费计算）
     */
    private static class OrderDataContext {
        private TmsOrderPriceCalculationVo orderVo;
        private TmsCustomerOrderEntity orderEntity;
        private List<String> subOrderNumbers; // 子单号列表
        private Map<String, TmsSortingRecordEntity> subOrderSortingRecords; // 子单号对应的分拣记录

        public OrderDataContext(TmsOrderPriceCalculationVo orderVo) {
            this.orderVo = orderVo;
            this.subOrderNumbers = new ArrayList<>();
            this.subOrderSortingRecords = new HashMap<>();
        }

        public TmsOrderPriceCalculationVo getOrderVo() { return orderVo; }
        public void setOrderVo(TmsOrderPriceCalculationVo orderVo) { this.orderVo = orderVo; }

        public TmsCustomerOrderEntity getOrderEntity() { return orderEntity; }
        public void setOrderEntity(TmsCustomerOrderEntity orderEntity) { this.orderEntity = orderEntity; }

        public List<String> getSubOrderNumbers() { return subOrderNumbers; }
        public void setSubOrderNumbers(List<String> subOrderNumbers) { this.subOrderNumbers = subOrderNumbers; }

        public Map<String, TmsSortingRecordEntity> getSubOrderSortingRecords() { return subOrderSortingRecords; }
        public void setSubOrderSortingRecords(Map<String, TmsSortingRecordEntity> subOrderSortingRecords) { this.subOrderSortingRecords = subOrderSortingRecords; }
    }

    /**
     * 计算附加费并返回详细信息（包含总额和明细）
     *
     * @param orderVo 订单信息
     * @param providerId 服务商ID
     * @param basePrice 基础运费
     * @param sourceChannel 来源渠道
     * @return 附加费计算结果（包含总额和明细）
     */
    private AdditionalFeeCalculationResult calculateAdditionalFeesWithDetail(TmsOrderPriceCalculationVo orderVo, Long providerId, BigDecimal basePrice, Integer sourceChannel) {
        AdditionalFeeCalculationResult result = new AdditionalFeeCalculationResult();

        try {
            // 查询该服务商下的所有有效附加费规则
            List<TmsFeeRuleEntity> feeRules = tmsFeeRuleService.getValidFeeRulesByProviderId(providerId);
            if (CollUtil.isEmpty(feeRules)) {
                log.debug("服务商 {} 未配置附加费规则", providerId);
                return result;
            }

            // 根据来源渠道构建订单数据上下文
            OrderDataContext orderContext = buildOrderDataContext(orderVo, sourceChannel);
            if (orderContext == null) {
                log.warn("订单 {} 无法构建订单数据上下文，跳过附加费计算", orderVo.getCustomerOrderNumber());
                return result;
            }

            // 遍历匹配附加费
            for (TmsFeeRuleEntity feeRule : feeRules) {
                // 执行匹配方法
                BigDecimal feeAmount = calculateSingleFeeRuleOptimized(orderContext, feeRule, basePrice);
                // 如果有匹配的费用，则添加到结果中，进行附加费累加
                if (feeAmount.compareTo(BigDecimal.ZERO) > 0) {
                    result.addFeeItem(feeRule.getId().toString(), feeAmount);
                    log.debug("订单 {} 应用附加费规则: {}, 费用: {}", orderVo.getCustomerOrderNumber(), feeRule.getFeeName(), feeAmount);
                }
            }

            log.debug("订单 {} 附加费计算完成，总附加费: {}, 明细: {}", orderVo.getCustomerOrderNumber(), result.getTotalFee(), result.getSurchargeDetail());
            return result;

        } catch (Exception e) {
            log.error("订单 {} 附加费计算异常", orderVo.getCustomerOrderNumber(), e);
            // 记录异常日志
            if (sourceChannel != null && sourceChannel == 2) {
                String errorMessage = "附加费计算异常: " + e.getMessage();
                tmsOrderLogService.saveLog(orderVo.getEntrustedOrderNumber(), 404, errorMessage, "");
            }
            return result;
        }
    }








}
