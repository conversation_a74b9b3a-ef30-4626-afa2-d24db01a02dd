package com.jygjexp.jynx.tms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.tms.entity.TmsOrderFeeDetailsEntity;
import com.jygjexp.jynx.tms.mapper.TmsOrderFeeDetailsMapper;
import com.jygjexp.jynx.tms.service.TmsOrderFeeDetailsService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 订单费用明细表
 *
 * <AUTHOR>
 * @date 2025-09-01 16:27:11
 */
@Service
public class TmsOrderFeeDetailsServiceImpl extends ServiceImpl<TmsOrderFeeDetailsMapper, TmsOrderFeeDetailsEntity> implements TmsOrderFeeDetailsService {

    @Override
    public void saveOrderFeeDetails(String orderNo, BigDecimal forecastBaseFreight, BigDecimal forecastTax, BigDecimal forecastAdditionalFee, BigDecimal forecastTotalFee) {
        TmsOrderFeeDetailsEntity entity = new TmsOrderFeeDetailsEntity();
        entity.setOrderNo(orderNo);
        entity.setForecastBaseFreight(forecastBaseFreight);
        entity.setForecastTax(forecastTax);
        entity.setForecastAdditionalFee(forecastAdditionalFee);
        entity.setForecastTotalFee(forecastTotalFee);
        this.save(entity);
    }
}