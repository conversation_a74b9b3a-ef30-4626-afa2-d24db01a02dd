package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.*;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderEntity;
import com.jygjexp.jynx.tms.vo.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;


public interface TmsStoreOrderService extends MPJBaseService<TmsStoreOrderEntity> {


    Page<StoreOrderVO> selectPage(Page page, StoreOrderQueryDTO storeOrderQueryDTO);

    List<StoreOrderExcelVO> exportStoreOrder(Page page, StoreOrderQueryDTO storeOrderQueryDTO);

    R saveStoreOrder(StoreOrderDTO storeOrderDTO);

    boolean updateStoreOrderById(StoreOrderDTO storeOrderDTO);

    StoreOrderDetailVO getStoreOrderById(Long id);

    List<ImportStoreGoodsDTO> importStoreGoods(MultipartFile file);

    /**
     * 推荐服务商
     * @param channelPriceQueryDTO
     * @return
     */
    ChannelPriceVO getSuggestChannelPrice(ChannelPriceQueryDTO channelPriceQueryDTO);

    /**
     * 其它服务商
     * @param channelPriceQueryDTO
     * @return
     */
    List<ChannelPriceVO> getChannelPrice(ChannelPriceQueryDTO channelPriceQueryDTO);

    SuggestStoreDetailVO getSuggestStore(SuggestStoreQueryDTO storeQueryDTO);

    PromotionCodeVO checkPromotionCode(PromotionCodeDTO codeDTO);

    void printScript(Long id,HttpServletResponse response);

    IPage<StoreOrderVO> getTmsStoreOrderStorePage(StoreOrderQueryDTO storeOrderQueryDTO);

    IPage<StoreOrderVO> getTmsStoreOrderAdminPage(StoreOrderQueryDTO storeOrderQueryDTO);

    R<List<TmsImportStoreDTO>> importCargoInfo(MultipartFile file);

    boolean cancelOrder(Long id);
    /**
     * 核销订单
     *
     * @param writeOffDTO
     * @return
     */
    R<TmsStoreOrderEntity> writeOffOrder(StoreOrderWriteOffDTO writeOffDTO);

    /**
     * 查询待同步快递轨迹的子单
     * @param orderStatus
     * @param pushTime
     * @return
     */
    List<TmsStoreOrderEntity> selectSubStoreOrderByPushTime(List<Integer> orderStatus, LocalDateTime pushTime);


    /**
     * 根据单号子单号更改订单状态
     */
    void updateOrderStatusByEntrustedOrderNumber(String entrustedOrderNumber,Integer newOrderStatus);


    /**
     * 导入订单服务商询价
     */
    List<ChannelPriceVO> getImportOrderChannelPrice(List<ChannelPriceQueryDTO> channelPriceQueryDTO, String channelCode);

    R getStoreOrderByOrderNumber(String orderNumber);

    boolean saveStoreOrderBatch(List<StoreOrderDTO> storeOrderDTOs);

    List<StoreOrderListExcelVO> exportStoreOrderList(StoreOrderQueryDTO storeOrderQueryDTO);

    List<StoreOrderAdminExcelVO> exportStoreOrderAdmin(StoreOrderQueryDTO storeOrderQueryDTO);

    /**
     * 更新主订单状态,根据主单号
     * @param mainEntrustedOrder
     */
    void updateMainOrderStatus(String mainEntrustedOrder);

    /**
     * 加密操作 - 预留给异常处理-删除小包订单
     */
    boolean deletePackageOrder(PackageOrderSecretDTO secretDTO);
    /**
     * 加密操作 - 预留给异常处理-单独推送小包订单
     */
    boolean pushSinglePackageOrder(PackageOrderSecretDTO secretDTO);

    R<String> getLabel(Long id);
}
