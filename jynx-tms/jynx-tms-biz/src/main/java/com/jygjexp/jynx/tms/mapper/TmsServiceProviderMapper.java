package com.jygjexp.jynx.tms.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.common.data.datascope.JynxBaseMapper;
import com.jygjexp.jynx.tms.constants.StoreConstants;
import com.jygjexp.jynx.tms.entity.TmsServiceProviderEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TmsServiceProviderMapper extends JynxBaseMapper<TmsServiceProviderEntity> {


    default List<TmsServiceProviderEntity> getAllValid(){
        LambdaQueryWrapper<TmsServiceProviderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsServiceProviderEntity::getIsValid, StoreConstants.ONE);
        return selectList(queryWrapper);
    }

}
