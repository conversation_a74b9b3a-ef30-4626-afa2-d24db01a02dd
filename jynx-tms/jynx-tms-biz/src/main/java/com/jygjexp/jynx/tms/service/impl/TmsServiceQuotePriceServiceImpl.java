package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.TmsServiceQuoteDetailRegionDto;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.mapper.TmsServiceQuotePriceMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsServiceQuotePriceService;
import com.jygjexp.jynx.tms.vo.TmsServiceQuotePageVo;
import com.jygjexp.jynx.tms.vo.TmsServiceQuotePricePageVo;
import com.jygjexp.jynx.tms.vo.excel.TmsServiceQuotePriceExcelVo;
import com.jygjexp.jynx.zxoms.entity.NbWeightDetailEntity;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * 服务商报价-价格配置子表
 *
 * <AUTHOR>
 * @date 2025-07-09 18:33:18
 */
@RequiredArgsConstructor
@Service
public class TmsServiceQuotePriceServiceImpl extends ServiceImpl<TmsServiceQuotePriceMapper, TmsServiceQuotePriceEntity> implements TmsServiceQuotePriceService {

    private final TmsServiceQuotePriceMapper tmsServiceQuotePriceMapper;

    // 服务商报价配置分页查询
    @Override
    public Page<TmsServiceQuoteDetailRegionDto> search(Page page, TmsServiceQuotePricePageVo vo) {
        MPJLambdaWrapper<TmsServiceQuotePriceEntity> queryWrapper = new MPJLambdaWrapper<TmsServiceQuotePriceEntity>();
        queryWrapper
                .selectAll(TmsServiceQuotePriceEntity.class)
                .like(ObjectUtil.isNotNull(vo.getRegionName()),TmsServiceQuotePriceEntity::getRegionName, vo.getRegionName())
                .eq(ObjectUtil.isNotNull(vo.getIsValid()),TmsServiceQuotePriceEntity::getIsValid, vo.getIsValid())
                .like(StrUtil.isNotBlank(vo.getCreateBy()),TmsServiceQuotePriceEntity::getCreateBy, vo.getCreateBy())
                .eq(ObjectUtil.isNotNull(vo.getQuoteId()),TmsServiceQuotePriceEntity::getQuoteId, vo.getQuoteId())
                .between(StrUtil.isNotBlank(vo.getStartTime()) && StrUtil.isNotBlank(vo.getEndTime()),TmsServiceQuotePriceEntity::getCreateTime, vo.getStartTime(), vo.getEndTime())
                // 排序：先按纵区升序，再按起始重量升序
                .orderByAsc(TmsServiceQuotePriceEntity::getRegionName)
                .orderByAsc(TmsServiceQuotePriceEntity::getWeightStart);
        return tmsServiceQuotePriceMapper.selectJoinPage(page, TmsServiceQuoteDetailRegionDto.class, queryWrapper);
    }

    // 服务商报价配置新增
    @Override
    public R saveServiceQuotePrice(TmsServiceQuotePriceEntity tmsServiceQuotePrice) {
        //  校验重量区间是否重复、重叠、交叉
        String checkWeight = checkWeightRepetition(tmsServiceQuotePrice);
        if (StrUtil.isNotBlank(checkWeight)) {
            return R.failed(checkWeight);
        }
        return R.ok(tmsServiceQuotePrice.insert());
    }

    // 服务商报价配置修改
    @Override
    public R updateServiceQuotePriceById(TmsServiceQuotePriceEntity tmsServiceQuotePrice) {
        //  导入校验重量区间是否重复、重叠、交叉
        String checkWeight = checkWeightRepetition(tmsServiceQuotePrice);
        if (StrUtil.isNotBlank(checkWeight)) {
            return R.failed(checkWeight);
        }
        return R.ok(tmsServiceQuotePrice.updateById());
    }

    // 服务商报价配置数据导出
    @Override
    public List<TmsServiceQuotePriceExcelVo> getServiceQuotePriceExport(TmsServiceQuotePricePageVo vo, Long[] ids) {
        MPJLambdaWrapper<TmsServiceQuotePriceEntity> queryWrapper = new MPJLambdaWrapper<TmsServiceQuotePriceEntity>();
        queryWrapper
                .selectAll(TmsServiceQuotePriceEntity.class)
                .like(ObjectUtil.isNotNull(vo.getRegionName()),TmsServiceQuotePriceEntity::getRegionName, vo.getRegionName())
                .eq(ObjectUtil.isNotNull(vo.getIsValid()),TmsServiceQuotePriceEntity::getIsValid, vo.getIsValid())
                .like(StrUtil.isNotBlank(vo.getCreateBy()),TmsServiceQuotePriceEntity::getCreateBy, vo.getCreateBy())
                .eq(ObjectUtil.isNotNull(vo.getQuoteId()),TmsServiceQuotePriceEntity::getQuoteId, vo.getQuoteId())
                .in(ObjectUtil.isNotEmpty(ids) && ids.length > 0, TmsTransportTaskOrderEntity::getId, ids)
                .between(StrUtil.isNotBlank(vo.getStartTime()) && StrUtil.isNotBlank(vo.getEndTime()),TmsServiceQuotePriceEntity::getCreateTime, vo.getStartTime(), vo.getEndTime())
                // 排序：先按纵区升序，再按起始重量升序
                .orderByAsc(TmsServiceQuotePriceEntity::getRegionName)
                .orderByAsc(TmsServiceQuotePriceEntity::getWeightStart);
        return tmsServiceQuotePriceMapper.selectJoinList(TmsServiceQuotePriceExcelVo.class, queryWrapper);
    }

    // 服务商报价配置数据导入
    @Override
    public R processFile(MultipartFile file, Long quoteId, String regionName) {
        // 验证文件类型和空文件
        if (file == null || file.isEmpty() || !isExcelFile(file.getOriginalFilename())) {
            return LocalizedR.failed("order.upload.valid.file", Optional.ofNullable(null));
        }

        try (InputStream inputStream = file.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            List<List<Object>> lines = reader.read(1, reader.getRowCount()); // 跳过表头

            if (lines == null || lines.isEmpty()) {
                return LocalizedR.failed("order.upload.empty.data", Optional.ofNullable(null));
            }

            // 10000条记录的限制
            int maxRows = 10000;
            if (lines.size() > maxRows) {
                return LocalizedR.failed("tms.ServiceRegionDetail.file.too.many.rows", maxRows);
            }

            List<String> errorMessages = new ArrayList<>();
            List<TmsServiceQuotePriceEntity> weightList = new ArrayList<>();

            List<Pair<BigDecimal, BigDecimal>> tempValidList = new ArrayList<>();
            for (int i = 0; i < lines.size(); i++) {
                List<Object> line = lines.get(i);
                List<String> lineErrors = new ArrayList<>();

                BigDecimal startWeight = validateBigDecimalField(line, 0, "starting weight cannot be empty/起始重量不能为空", i + 1, lineErrors);
                BigDecimal endWeight = validateBigDecimalField(line, 1, "cut-off weight cannot be empty/结束重量不能为空", i + 1, lineErrors);
                BigDecimal price = validateBigDecimalField(line, 2, "price cannot be empty/价格不能为空", i + 1, lineErrors);
                String memberId = validateStringField(line, 3, "weight partition cannot be empty/会员等级不能为空", i + 1, lineErrors);
                BigDecimal profitMargin = validateBigDecimalField(line, 4, "profit margin cannot be empty/利润率不能为空", i + 1, lineErrors);

                if (startWeight != null && endWeight != null) {
                    validateWeightField(line, 0, 1, quoteId, regionName, "Weight interval repetition/重量区间重复", i + 1, lineErrors);
                    validateWeightRepetition(line, 0, 1, quoteId, regionName, "Whether the weight ranges overlap or intersect/重量区间是否重叠、交叉"
                            , i + 1, lineErrors,tempValidList);

                    if (startWeight.compareTo(endWeight) >= 0) {
                        lineErrors.add("row" + (i + 1) + "：The cut-off weight cannot be less than the starting weight/结束重量不能小于或等于起始重量");
                    }
                }

                if (!lineErrors.isEmpty()) {
                    errorMessages.addAll(lineErrors);
                } else {
                    TmsServiceQuotePriceEntity weightDetail = new TmsServiceQuotePriceEntity();
                    weightDetail.setQuoteId(quoteId);
                    weightDetail.setRegionName(regionName);
                    weightDetail.setWeightStart(startWeight);
                    weightDetail.setWeightEnd(endWeight);
                    weightDetail.setPrice(price);

                    Integer authValue = 0;
                    switch (memberId) {
                        case "Ordinary Card Member":
                            authValue = 1;
                            break;
                        case "Silver Membership Details":
                            authValue = 2;
                            break;
                        case "gold card member":
                            authValue = 3;
                            break;
                    }
                    weightDetail.setMemberLevel(authValue);
                    weightDetail.setProfitRate(profitMargin);
                    weightList.add(weightDetail);
                }
            }

            // 校验本次导入的 weightList 中是否存在交叉重叠
            List<String> localConflictErrors = validateLocalWeightOverlap(weightList);
            if (!localConflictErrors.isEmpty()) {
                return R.failed("本次导入数据中存在区间重叠:<br>" + String.join("<br>", localConflictErrors));
            }

            // 保存新增数据
            Integer weightCount = processWeights(weightList);
            String successMessage = "Successfully processed: " + weightCount + " rows.";

            if (errorMessages.isEmpty()) {
                return LocalizedR.ok("tms.ServiceQuotePrice.note.Excel.file.processing.success", successMessage);
            } else {
                return LocalizedR.failed("tms.ServiceQuotePrice.file.processing.errors",
                        successMessage + "<br>Errors:<br>" + String.join("<br>", errorMessages));
            }

        } catch (IOException e) {
            log.error("导入价格明细Excel文件处理异常", e);
            return LocalizedR.failed("tms.ServiceQuotePrice.Excel.file.processing.exception", e.getMessage());
        }
    }

    private List<String> validateLocalWeightOverlap(List<TmsServiceQuotePriceEntity> weights) {
        List<String> errors = new ArrayList<>();

        // 按起始重量升序排序
        weights.sort(Comparator.comparing(TmsServiceQuotePriceEntity::getWeightStart));

        for (int i = 0; i < weights.size(); i++) {
            TmsServiceQuotePriceEntity current = weights.get(i);
            for (int j = i + 1; j < weights.size(); j++) {
                TmsServiceQuotePriceEntity next = weights.get(j);

                BigDecimal curStart = current.getWeightStart();
                BigDecimal curEnd = current.getWeightEnd();
                BigDecimal nextStart = next.getWeightStart();
                BigDecimal nextEnd = next.getWeightEnd();

                // 判断是否重叠（交叉）：只要当前的结束 > 下一个起始 && 当前起始 < 下一个结束
                if (curEnd.compareTo(nextStart) > 0 && curStart.compareTo(nextEnd) < 0) {
                    errors.add(String.format(
                            "区间重叠：[%s - %s] 与 [%s - %s]",
                            curStart.toPlainString(), curEnd.toPlainString(),
                            nextStart.toPlainString(), nextEnd.toPlainString()
                    ));
                }
            }
        }

        return errors;
    }





    // 校验文件是否是.xls或.xlsx格式
    private boolean isExcelFile(String fileName) {
        return fileName != null && (fileName.endsWith(".xls") || fileName.endsWith(".xlsx"));
    }

    //校验String类型字段是否为空
    private String validateStringField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null || value.toString().trim().isEmpty()) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }
        return value.toString().trim();
    }

    //校验Decimal类型字段是否为空
    private BigDecimal validateBigDecimalField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null || value.toString().trim().isEmpty()) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }
        try {
            BigDecimal decimal = new BigDecimal(value.toString().trim());

            // 新增：校验是否为负数
            if (decimal.compareTo(BigDecimal.ZERO) < 0) {
                lineErrors.add("row" + row + "：" + value + "（It cannot be a negative number）");
                return null;
            }

            return decimal;
        } catch (NumberFormatException e) {
            lineErrors.add("row" + row + "：" + value + "(format error)");
            return null;
        }
    }

    //校验重量区间是否重复
    private String validateWeightField(List<Object> line, int index, int index2,Long quoteId,String regionName, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        Object value2 = line.get(index2);

        List<TmsServiceQuotePriceEntity> weightEntities = tmsServiceQuotePriceMapper.selectList(new LambdaQueryWrapper<TmsServiceQuotePriceEntity>()
                .eq(TmsServiceQuotePriceEntity::getWeightStart, value)
                .eq(TmsServiceQuotePriceEntity::getWeightEnd, value2)
                .eq(TmsServiceQuotePriceEntity::getQuoteId,quoteId)
                .eq(TmsServiceQuotePriceEntity::getRegionName,regionName.trim())
                .last("limit 1"));
        if (CollUtil.isNotEmpty(weightEntities)) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }
        return value.toString().trim();
    }

    //处理批量导入重量明细
    public Integer processWeights(List<TmsServiceQuotePriceEntity> weightList) {
        Integer count = 0;
        for (TmsServiceQuotePriceEntity weight : weightList) {
            boolean save = this.save(weight);
            if (save) {
                count++;
            }
        }
        return count;
    }

    // 导入校验重量区间是否重叠、交叉
    private String validateWeightRepetition(List<Object> line, int index, int index2, Long quoteId, String regionName, String errorMessage,
            int row, List<String> lineErrors, List<Pair<BigDecimal, BigDecimal>> tempValidList) {

        Object startObj = line.get(index);
        Object endObj = line.get(index2);

        if (startObj == null || endObj == null) return null;

        try {
            BigDecimal start = new BigDecimal(startObj.toString().trim());
            BigDecimal end = new BigDecimal(endObj.toString().trim());

            // 校验是否与数据库中区间重叠
            List<TmsServiceQuotePriceEntity> existingList = tmsServiceQuotePriceMapper.selectList(
                    new LambdaQueryWrapper<TmsServiceQuotePriceEntity>()
                            .eq(TmsServiceQuotePriceEntity::getQuoteId, quoteId)
                            .eq(TmsServiceQuotePriceEntity::getRegionName, regionName.trim())
            );

            for (TmsServiceQuotePriceEntity entity : existingList) {
                BigDecimal existStart = entity.getWeightStart();
                BigDecimal existEnd = entity.getWeightEnd();
                if (existStart == null || existEnd == null) continue;

                if (start.compareTo(existEnd) < 0 && existStart.compareTo(end) < 0) {
                    lineErrors.add("row" + row + "：当前区间 [" + start + " - " + end + "] 与数据库中已存在区间 [" +
                            existStart + " - " + existEnd + "] 重叠");
                    return null;
                }
            }

            // 校验是否与当前 Excel 批次中的已校验区间重叠
            for (Pair<BigDecimal, BigDecimal> pair : tempValidList) {
                BigDecimal existStart = pair.getLeft();
                BigDecimal existEnd = pair.getRight();

                if (start.compareTo(existEnd) < 0 && existStart.compareTo(end) < 0) {
                    lineErrors.add("row" + row + "：当前区间 [" + start + " - " + end + "] 与导入文件中区间 [" +
                            existStart + " - " + existEnd + "] 重叠");
                    return null;
                }
            }

            // 添加当前合法区间用于后续校验
            tempValidList.add(Pair.of(start, end));

            return start.toString();

        } catch (NumberFormatException e) {
            lineErrors.add("row" + row + "：重量格式错误（" + startObj + ", " + endObj + "）");
            return null;
        }
    }


    // 新增、修改校验重量区间是否重叠、交叉
    private String checkWeightRepetition(TmsServiceQuotePriceEntity weight) {
        BigDecimal startObj = weight.getWeightStart();
        BigDecimal endObj = weight.getWeightEnd();

        // 防御性校验
        if (startObj == null || endObj == null) return null;

        try {
            List<TmsServiceQuotePriceEntity> weightEntities = tmsServiceQuotePriceMapper.selectList(new LambdaQueryWrapper<TmsServiceQuotePriceEntity>()
                    .eq(TmsServiceQuotePriceEntity::getWeightStart, startObj)
                    .eq(TmsServiceQuotePriceEntity::getWeightEnd, endObj)
                    .eq(TmsServiceQuotePriceEntity::getQuoteId,weight.getQuoteId())
                    .eq(TmsServiceQuotePriceEntity::getRegionName,weight.getRegionName().trim())
                    .ne(ObjectUtil.isNotNull(weight.getId()),TmsServiceQuotePriceEntity::getId, weight.getId())
                    .last("limit 1"));
            if (CollUtil.isNotEmpty(weightEntities)) {
                return "Weight interval repetition";
            }

            // 查询所有与该 quoteId & regionId 匹配的区间
            List<TmsServiceQuotePriceEntity> existingList = tmsServiceQuotePriceMapper.selectList(
                    new LambdaQueryWrapper<TmsServiceQuotePriceEntity>()
                            .eq(TmsServiceQuotePriceEntity::getQuoteId, weight.getQuoteId())
                            .eq(TmsServiceQuotePriceEntity::getRegionName, weight.getRegionName())
                            .ne(ObjectUtil.isNotNull(weight.getId()),TmsServiceQuotePriceEntity::getId, weight.getId())
            );

            for (TmsServiceQuotePriceEntity entity : existingList) {
                BigDecimal existStart = entity.getWeightStart();
                BigDecimal existEnd = entity.getWeightEnd();

                if (existStart == null || existEnd == null) continue;

                // 判断是否重叠
                if (startObj.compareTo(existEnd) <= 0 && existStart.compareTo(endObj) <= 0) {
                    return "Current interval ["+startObj+" - "+endObj+"] And the existing intervals ["+existStart+" - "+existEnd+"] overlap";
                }
            }
            return null;
        } catch (NumberFormatException e) {
            return null;
        }
    }



}