package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.TmsOrderBatchDto;
import com.jygjexp.jynx.tms.entity.TmsOrderBatchEntity;
import com.jygjexp.jynx.tms.vo.TmsCustomerOrderBatchVo;
import com.jygjexp.jynx.tms.vo.TmsOrderBatchPageVo;

public interface TmsOrderBatchService extends IService<TmsOrderBatchEntity> {

    // 批次管理分页
    Page<TmsOrderBatchPageVo> search(Page page, TmsOrderBatchPageVo vo);

    // 创建批次
    R addOrderBatch(TmsOrderBatchDto dto);

    // 创建空批次
    R createOrderBatch(TmsOrderBatchDto dto);

    // 分拣订单选择批次
    R selectOrderBatch(TmsOrderBatchDto dto);

    // 批次跟踪单详情
    Page<TmsCustomerOrderBatchVo> getDetailByBatchNo(Page page, String batchNo);

    // 修改批次号
    R updateBatchNo(TmsOrderBatchEntity tmsOrderBatch, String oldBatchNo);

    TmsOrderBatchEntity createForwardBatch(Integer isNewBatch);

    boolean checkBatchCreatedToday();

    boolean switchForwardBatch(Long id);
}