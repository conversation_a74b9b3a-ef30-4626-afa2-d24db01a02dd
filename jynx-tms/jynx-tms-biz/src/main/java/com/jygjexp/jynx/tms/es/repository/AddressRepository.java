package com.jygjexp.jynx.tms.es.repository;

import com.jygjexp.jynx.tms.es.document.AddressDocument;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.annotations.Query;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 地址Elasticsearch Repository
 *
 * <AUTHOR>
 * @date 2025-09-02
 */
@Repository
public interface AddressRepository extends ElasticsearchRepository<AddressDocument, String> {

    /**
     * 根据状态查询
     */
    List<AddressDocument> findByStatus(Integer status);

    /**
     * 多字段搜索 - 支持邮编、城市、街道的模糊匹配
     */
    @Query("""
        {
          "bool": {
            "must": [
              {
                "term": {
                  "status": 1
                }
              }
            ],
            "should": [
              {
                "prefix": {
                  "threePostCode": {
                    "value": "?0",
                    "boost": 3.0
                  }
                }
              },
              {
                "prefix": {
                  "sixPostCode": {
                    "value": "?0",
                    "boost": 2.5
                  }
                }
              },
              {
                "match": {
                  "city": {
                    "query": "?0",
                    "boost": 2.0,
                    "fuzziness": "AUTO"
                  }
                }
              },
              {
                "match": {
                  "street": {
                    "query": "?0",
                    "boost": 1.5,
                    "fuzziness": "AUTO"
                  }
                }
              },
              {
                "match": {
                  "fullAddress": {
                    "query": "?0",
                    "boost": 1.0
                  }
                }
              }
            ],
            "minimum_should_match": 1
          }
        }
        """)
    Page<AddressDocument> searchByKeyword(String keyword, Pageable pageable);

    /**
     * 自动补全搜索
     */
    @Query("""
        {
          "bool": {
            "must": [
              {
                "term": {
                  "status": 1
                }
              },
              {
                "multi_match": {
                  "query": "?0",
                  "type": "bool_prefix",
                  "fields": [
                    "suggest",
                    "suggest._2gram",
                    "suggest._3gram"
                  ]
                }
              }
            ]
          }
        }
        """)
    Page<AddressDocument> autocomplete(String keyword, Pageable pageable);
}
