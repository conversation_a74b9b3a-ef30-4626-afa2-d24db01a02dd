package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.tms.dto.TmsFinanceReceivablePageDto;
import com.jygjexp.jynx.tms.dto.TmsFinanceReceivableStoreDetailsDto;
import com.jygjexp.jynx.tms.dto.TmsFinanceReceivableStorePageDto;
import com.jygjexp.jynx.tms.excel.TmsFinanceReceivableExcelDto;
import com.jygjexp.jynx.tms.excel.TmsFinanceReceivableStoreExcelDto;
import com.jygjexp.jynx.tms.vo.TmsFinanceReceivablePageVo;
import com.jygjexp.jynx.tms.vo.TmsFinanceReceivableStorePageVo;

import java.util.List;

/**
 * @Description
 * @Date 2025/8/5 11:37
 * @Created guqingren
 */
public interface TmsFinanceReceivableService {


    /**
     * 管理端-分页
     *
     * @param vo
     * @return
     */
    Page<TmsFinanceReceivablePageDto> search(TmsFinanceReceivablePageVo vo);

    /**
     * 管理端-详情
     *
     * @param storeId
     * @return
     */
    TmsFinanceReceivablePageDto details(Long storeId, String billingCycle);

    /**
     * 管理端-详情分页
     * @param storeId
     * @param current
     * @param size
     * @return
     */
    Page<TmsFinanceReceivableStorePageDto> detailsPage(Long storeId, Integer current, Integer size);

    /**
     * 管理端-导出
     * @param vo
     * @return
     */
    List<TmsFinanceReceivableExcelDto> export(TmsFinanceReceivablePageVo vo);

    /**
     * 门店端-分页
     *
     * @param vo
     * @return
     */
    Page<TmsFinanceReceivableStorePageDto> storeSearch(TmsFinanceReceivableStorePageVo vo);

    /**
     * 门店端-详情
     *
     * @param orderId
     * @return
     */
    TmsFinanceReceivableStoreDetailsDto storeDetails(Long orderId);

    /**
     * 门店端-导出
     * @param vo
     * @return
     */
    List<TmsFinanceReceivableStoreExcelDto> storeExport(TmsFinanceReceivableStorePageVo vo);
}
