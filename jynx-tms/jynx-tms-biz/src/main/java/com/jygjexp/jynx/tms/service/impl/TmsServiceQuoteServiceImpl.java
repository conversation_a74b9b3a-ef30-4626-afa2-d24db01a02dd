package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.query.MPJLambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.TmsServiceQuoteDetailDto;
import com.jygjexp.jynx.tms.dto.TmsServiceQuoteDto;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.mapper.TmsServiceQuoteMapper;
import com.jygjexp.jynx.tms.service.TmsServiceQuoteService;
import com.jygjexp.jynx.tms.vo.TmsServiceQuotePageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
/**
 * 服务商报价表
 *
 * <AUTHOR>
 * @date 2025-07-09 17:49:14
 */
@RequiredArgsConstructor
@Service
public class TmsServiceQuoteServiceImpl extends ServiceImpl<TmsServiceQuoteMapper, TmsServiceQuoteEntity> implements TmsServiceQuoteService {
    private final TmsServiceQuoteMapper serviceQuoteMapper;

    // 服务商报价分页查询
    @Override
    public Page<TmsServiceQuoteDto> search(Page page, TmsServiceQuotePageVo vo) {
        MPJLambdaWrapper<TmsServiceQuoteEntity> queryWrapper = new MPJLambdaWrapper<TmsServiceQuoteEntity>();
        queryWrapper
                .select("t.*,t1.provider_name as providerName,t2.region_name as reachableRegionName,t3.region_name as unreachableRegionName,t4.channel_name as channelName")
                .leftJoin(TmsServiceProviderEntity.class, TmsServiceProviderEntity::getProviderId, TmsServiceQuoteEntity::getProviderId)
                .leftJoin(TmsServiceRegionEntity.class, TmsServiceRegionEntity::getRegionId, TmsServiceQuoteEntity::getReachableRegionId)
                .leftJoin(TmsServiceRegionEntity.class, TmsServiceRegionEntity::getRegionId, TmsServiceQuoteEntity::getUnreachableRegionId)
                .leftJoin(TmsChannel.class, TmsChannel::getId, TmsServiceQuoteEntity::getChannelId)
                .eq(ObjectUtil.isNotNull(vo.getProviderId()),TmsServiceProviderEntity::getProviderId, vo.getProviderId())
                .like(StrUtil.isNotBlank(vo.getQuoteCode()),TmsServiceQuoteEntity::getQuoteCode, vo.getQuoteCode())
                .like(StrUtil.isNotBlank(vo.getQuoteName()),TmsServiceQuoteEntity::getQuoteName, vo.getQuoteName())
                .between(StrUtil.isNotBlank(vo.getStartTime()) && StrUtil.isNotBlank(vo.getEndTime()),TmsServiceQuoteEntity::getCreateTime, vo.getStartTime(), vo.getEndTime())
                .orderByDesc(TmsServiceQuoteEntity::getCreateTime);
        return serviceQuoteMapper.selectJoinPage(page, TmsServiceQuoteDto.class, queryWrapper);
    }

    // 根据报价id查询详情明细
    @Override
    public R getQuotePriceById(Long id) {
        MPJLambdaWrapper<TmsServiceQuoteEntity> queryWrapper = new MPJLambdaWrapper<TmsServiceQuoteEntity>();
        queryWrapper.select("t.*,t1.provider_name as providerName,t2.region_name as reachableRegionName,t3.region_name as unreachableRegionName")
                .leftJoin(TmsServiceProviderEntity.class, TmsServiceProviderEntity::getProviderId, TmsServiceQuoteEntity::getProviderId)
                .leftJoin(TmsServiceRegionEntity.class, TmsServiceRegionEntity::getRegionId, TmsServiceQuoteEntity::getReachableRegionId)
                .leftJoin(TmsServiceRegionEntity.class, TmsServiceRegionEntity::getRegionId, TmsServiceQuoteEntity::getUnreachableRegionId)
                .eq(TmsServiceQuoteEntity::getId, id);
        return R.ok(serviceQuoteMapper.selectJoinOne(TmsServiceQuoteDto.class, queryWrapper));
    }
}