package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.tms.entity.TmsStoreProfitConfigDetailEntity;
import com.jygjexp.jynx.tms.mapper.TmsStoreProfitConfigDetailMapper;
import com.jygjexp.jynx.tms.service.TmsStoreProfitConfigDetailService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 服务商利润配置重量段明细
 *
 * <AUTHOR>
 * @date 2025-08-26 10:46:59
 */
@Service
public class TmsStoreProfitConfigDetailServiceImpl extends ServiceImpl<TmsStoreProfitConfigDetailMapper, TmsStoreProfitConfigDetailEntity> implements TmsStoreProfitConfigDetailService {


    @Override
    public Map<Long, List<TmsStoreProfitConfigDetailEntity>> getGroupByProfitIds(List<Long> profitIds) {
        if(CollUtil.isEmpty(profitIds)){
            return MapUtil.empty();
        }
        LambdaQueryWrapper<TmsStoreProfitConfigDetailEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TmsStoreProfitConfigDetailEntity::getProfitId, profitIds);
        List<TmsStoreProfitConfigDetailEntity> entityList = baseMapper.selectList(queryWrapper);
        if(CollUtil.isEmpty(entityList)){
            return MapUtil.empty();
        }
        return entityList.stream().collect(Collectors.groupingBy(TmsStoreProfitConfigDetailEntity::getProfitId));
    }
}
