package com.jygjexp.jynx.tms.utils;


import com.jygjexp.jynx.tms.vo.EstimatedPriceBo;

import java.math.BigDecimal;

/**
 * 询价参数校验
 */

public class EstimatedPriceBoValidator {
    public static String validate(EstimatedPriceBo bo) {
        StringBuilder errors = new StringBuilder();

        // 校验邮编（去空格后长度 ≤ 6）
        if (bo.getShipperPostalCode() != null) {
            String trimmed = bo.getShipperPostalCode().replaceAll("\\s+", "");
            if (trimmed.length() > 6) {
                errors.append("发货人邮编去除空格后长度不能超过6位;");
            }
        }

        if (bo.getDestPostalCode() != null) {
            String trimmed = bo.getDestPostalCode().replaceAll("\\s+", "");
            if (trimmed.length() > 6) {
                errors.append("收货人邮编去除空格后长度不能超过6位;");
            }
        }

        // 校验保留3位小数
        if (!hasMaxThreeDecimalPlaces(bo.getTotalWeight())) {
            errors.append("总重量最多保留3位小数;");
        }

//        if (!hasMaxThreeDecimalPlaces(bo.getTotalVolume())) {
//            errors.append("总体积最多保留3位小数;");
//        }

        return errors.toString();
    }

    /**
     * 判断 BigDecimal 是否最多保留3位小数
     */
    private static boolean hasMaxThreeDecimalPlaces(BigDecimal value) {
        if (value == null) return true; // 可选字段视为通过
        return value.scale() <= 3;
    }
}
