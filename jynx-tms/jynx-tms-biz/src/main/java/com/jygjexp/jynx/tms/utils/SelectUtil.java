package com.jygjexp.jynx.tms.utils;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.constants.StoreEnums;
import com.jygjexp.jynx.tms.dto.StoreCustomerExtendDTO;
import com.jygjexp.jynx.tms.dto.StoreMessageTraceDTO;
import com.jygjexp.jynx.tms.dto.StoreOrderEmailTemplateDTO;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.enums.RegionEnums;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.vo.GeocodeResult;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Data
@Slf4j
@RequiredArgsConstructor
@Component
public class SelectUtil {

    public static SelectUtil th;


    @PostConstruct
    public void init() {
        th = this;
    }


    // region 客户订单(tms_customer_order)
    private final TmsCustomerOrderService customerOrderService;

    /**
     * 获取系统订单号
     */
    public static String getOrderNo(Long customerId, Integer businessModel) {
        return th.customerOrderService.generalNewOrderNo(customerId, businessModel);
    }

    /**
     * 根据地址 + 邮政编码 获取经纬度
     */
    public static GeocodeResult getLatLngByAddress(String address, String code){
        return th.customerOrderService.getLatLngByAddress(address,code);
    }

    /**
     * 查询单号是否存在
     */
    public static Boolean isOrderNoExist(String orderNo) {
        long count = th.customerOrderService.count(
                Wrappers.<TmsCustomerOrderEntity>lambdaQuery()
                        .nested(w -> w
                                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                                .eq(TmsCustomerOrderEntity::getSubFlag, true)
                        )
                        .or()
                        .eq(TmsCustomerOrderEntity::getCustomerOrderNumber, orderNo)
        );
        return count > 0;
    }





    // endregion


    // region 覆盖区域(tms_over_area)
    private final TmsOverAreaService tmsOverAreaService;

    public static List<TmsOverAreaEntity> getOverAreaList(Integer portCode){
        List<TmsBigAreaEntity> bigAreaList = getBigAreaList(portCode);
        if(CollUtil.isEmpty(bigAreaList)){
            return new ArrayList<>();
        }
        List<Long> longList = bigAreaList.stream().map(TmsBigAreaEntity::getId).collect(Collectors.toList());
        return th.tmsOverAreaService.list(Wrappers.<TmsOverAreaEntity>lambdaQuery().in(TmsOverAreaEntity::getBigAreaId, longList));
    }

    /**
     * 获取所有的前三位邮政编码
     */
    public static List<String> getAllFirstCode() {
        return th.tmsOverAreaService.list().stream()
                .flatMap(e -> Arrays.stream(e.getZip().split(",")))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .distinct()
                .collect(Collectors.toList());
    }


    // endregion


    // region 客户信息(tms_customer)
    private final TmsCustomerService customerService;

    public static TmsCustomerEntity getCustomerByUserId(Long userId) {
        return th.customerService.getOne(
                Wrappers.<TmsCustomerEntity>lambdaQuery().eq(TmsCustomerEntity::getUserId, userId).last("LIMIT 1"),
                false
        );
    }

    public static Map<Long,TmsCustomerEntity> getCustomerMap(){
        return th.customerService.list().stream().collect(Collectors.toMap(TmsCustomerEntity::getId, Function.identity()));
    }

    public static Map<Long,String> getCustomerNameMap(){
        return th.customerService.list().stream().collect(Collectors.toMap(TmsCustomerEntity::getId, TmsCustomerEntity::getCustomerName));
    }

    public static Map<String, TmsCustomerEntity> getCustomerByNameMap() {
        return th.customerService.list(Wrappers.emptyWrapper())
                .stream()
                .collect(Collectors.toMap(
                        TmsCustomerEntity::getCustomerName,
                        customer -> customer,
                        (existing, replacement) -> existing
                ));
    }


    // endregion


    // region 大区信息(tms_big_area)
    private final TmsBigAreaService bigAreaService;

    // 口岸仓库 匹配 大区地区名称
    public static List<TmsBigAreaEntity> getBigAreaList(Integer portCode){
        RegionEnums regionEnum = RegionEnums.getInstance(portCode);
        if (regionEnum == null) {
            return Collections.emptyList();
        }
        return th.bigAreaService.list(Wrappers.<TmsBigAreaEntity>lambdaQuery()
                .eq(TmsBigAreaEntity::getAreaName, regionEnum.getRegion()));
    }


    // endregion


    // region 司机(tms_lmd_driver)
    private final TmsLmdDriverService lmdDriverService;

    public static Map<Long, TmsLmdDriverEntity> getDriverMap() {
        return th.lmdDriverService.list().stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, Function.identity()));
    }
    // endregion


    // region 运输任务(tms_transport_task)

    private final TmsTransportTaskOrderService transportTaskOrderService;

    /**
     * 最后派送订单完成时间
     */
    public static LocalDateTime getLastDeliveryTime(Long driverId) {
        List<TmsTransportTaskOrderEntity> list = th.transportTaskOrderService.list(Wrappers.<TmsTransportTaskOrderEntity>lambdaQuery()
                .eq(TmsTransportTaskOrderEntity::getDriverId, driverId)
                .eq(TmsTransportTaskOrderEntity::getTaskStatus, 25003)
                .orderByDesc(TmsTransportTaskOrderEntity::getUpdateTime));
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.get(0).getUpdateTime();
    }

    // endregion

    /**
     * 获取当前用户的姓名
     */
    public static String getUserName() {
        return SecurityUtils.getUser().getName();
    }

    private final TmsStoreCustomerService tmsStoreCustomerService;
    /**
     * 根据客户ID获取快递客户名称
     */
    public static String getCustomerNameById(Long storeCustomerId) {
        if (storeCustomerId == null) {
            return null;
        }
        TmsStoreCustomerEntity customer = th.tmsStoreCustomerService.getById(storeCustomerId);
        if (customer == null) {
            return null;
        }
        return customer.getName();
    }

    /**
     * 校验是否打开对应的通知
     */
    public static boolean checkNoticeOpen(Long storeCustomerId, StoreEnums.StoreOrderEmail.EmailType orderEmailStatus) {
        TmsStoreCustomerEntity tmsStoreCustomerEntity = th.tmsStoreCustomerService.getById(storeCustomerId);
        if (null == tmsStoreCustomerEntity) {
            return false;
        }

        String extend = tmsStoreCustomerEntity.getExtend();
        if (StrUtil.isBlank(extend)) {
            return false;
        }

        try {
            // 解析最外层的 StoreCustomerExtendDTO
            StoreCustomerExtendDTO extendDTO = JSONObject.parseObject(extend, StoreCustomerExtendDTO.class);
            if (extendDTO == null || extendDTO.getNotifyConfig() == null) {
                return false;
            }

            // 获取通知配置
            StoreMessageTraceDTO notifyConfig = extendDTO.getNotifyConfig();

            Integer enabledValue = getNoticeEnabledValue(notifyConfig, orderEmailStatus);
            return enabledValue != null && enabledValue == 1;

        } catch (Exception e) {
            return false;
        }
    }
    /**
     * 根据通知类型获取对应字段的启用状态
     */
    private static Integer getNoticeEnabledValue(StoreMessageTraceDTO dto, StoreEnums.StoreOrderEmail.EmailType orderEmailStatus) {
        switch (orderEmailStatus) {
            case ORDER_CREATE:
                return dto.getOrderEmailEnabled();
            case ORDER_VERIFIED:
                return dto.getOrderSuccessEnabled();
            case ORDER_SHIPPING:
                return dto.getTransportEnabled();
            case ORDER_CONFIRMATION:
                return dto.getSignedEnabled();
            case ORDER_EXCEPTION:
                return dto.getOrderExceptionEnabled();
            case ORDER_CANCEL:
                return dto.getOrderCancelEnabled();
            default:
                return null;
        }
    }

    // 获取邮件参数
    private final TmsStoreOrderService tmsStoreOrderService;
    private final TmsStoreService tmsStoreService;

    /**
     * 构建邮件发送参数
     */
    public static StoreOrderEmailTemplateDTO buildEmailTemplateDTO(Long orderId) {
        if (orderId == null) {
            return null;
        }
        // 订单数据
        TmsStoreOrderEntity storeOrder = th.tmsStoreOrderService.getById(orderId);

        // 客户信息
        TmsStoreCustomerEntity storeCustomer = null;
        if (storeOrder.getStoreCustomerId() != null) {
            storeCustomer = th.tmsStoreCustomerService.getById(storeOrder.getStoreCustomerId());
        }

        if (storeCustomer == null) {
            return null;
        }
        // 获取门店地址
        String storeAddress = "";
        if (storeOrder.getStoreId() != null){
            storeAddress = th.tmsStoreService.getById(storeOrder.getStoreId()).getStoreAddress();
        }

        StoreOrderEmailTemplateDTO emailTemplateDTO = new StoreOrderEmailTemplateDTO();
        emailTemplateDTO.setEntrustedOrderNumber(storeOrder.getEntrustedOrderNumber());
        emailTemplateDTO.setDeliveryAddress(storeOrder.getReceiverAddress());
        emailTemplateDTO.setContactPhone(storeOrder.getReceiverPhone());
        emailTemplateDTO.setCustomerName(storeCustomer.getName());
        emailTemplateDTO.setStoreAddress(storeAddress);
        emailTemplateDTO.setSendEmail(storeCustomer.getEmail());

        return emailTemplateDTO;
    }
}
