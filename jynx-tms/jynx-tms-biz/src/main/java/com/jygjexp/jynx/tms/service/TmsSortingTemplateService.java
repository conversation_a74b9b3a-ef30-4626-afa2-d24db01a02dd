package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.TmsGridAndSortingTemplateDto;
import com.jygjexp.jynx.tms.dto.TmsSortingTemplateDto;
import com.jygjexp.jynx.tms.dto.TmsSortingTemplatePageDto;
import com.jygjexp.jynx.tms.entity.TmsSortingTemplateEntity;

import java.util.List;

public interface TmsSortingTemplateService extends IService<TmsSortingTemplateEntity> {


    /**
     * 新增分拣模版表
     */
    R add(TmsSortingTemplateDto tmsSortingTemplateDto);

    R getRouteNumberPage(Page page,String routeNumber, Long warehouseNameId);

    R updateRuleById(TmsSortingTemplateDto tmsSortingTemplateDto);

    /**
     * 启停模板
     *
     * @param id
     * @param isEnable
     * @param isNewBatch
     * @return
     */
    boolean changeEnable(Long id, Integer isEnable, Integer isNewBatch);

    /**
     * 删除模板
     * @param id
     * @return
     */
    boolean removeTemplate(Long id);

    R pageSearch(Page page, TmsSortingTemplatePageDto tmsSortingTemplatePageDto);

    R removeBatchByIdList(List<Long> ids);

    R getGridAndTemplate();

    R addGridAndSortingTemplate(List<TmsGridAndSortingTemplateDto> tmsGridAndSortingTemplateDtos);

    R getAllRouteNumber();

    R getSortingTemplateById(Long id);

    /**
     * 获取此时时启用的唯一的分拣模版
     */
    TmsSortingTemplateDto getEnableSortingTemplate();

    boolean isGenerateBatch();
}
