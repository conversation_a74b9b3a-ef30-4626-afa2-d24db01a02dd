package com.jygjexp.jynx.tms.service;

import com.jygjexp.jynx.tms.document.AddressDocument;
import com.jygjexp.jynx.tms.entity.TmsBaseCityStreetPostcodeEntity;
import com.jygjexp.jynx.tms.repository.AddressRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;

import java.util.List;

/**
 * 地址Elasticsearch搜索服务
 *
 * <AUTHOR>
 * @date 2025-09-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AddressSearchService {

    private final AddressRepository addressRepository;

    /**
     * 使用Elasticsearch进行地址自动补全搜索
     * 
     * @param keyword 搜索关键词
     * @param limit 返回结果数量限制
     * @return 搜索结果列表
     */
    public List<AddressDocument> searchAddressWithElasticsearch(String keyword, Integer limit) {
        if (StrUtil.isBlank(keyword)) {
            return List.of();
        }
        
        // 限制返回结果数量
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        if (limit > 50) {
            limit = 50;
        }
        
        try {
            // 创建分页对象
            Pageable pageable = PageRequest.of(0, limit);
            
            // 执行搜索
            Page<AddressDocument> searchResult = addressRepository.searchByKeyword(
                keyword.trim().toUpperCase(), pageable);
            
            return searchResult.getContent();
            
        } catch (Exception e) {
            log.error("Elasticsearch搜索失败，关键词: {}, 错误: {}", keyword, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 自动补全搜索
     */
    public List<AddressDocument> autocompleteSearch(String keyword, Integer limit) {
        if (StrUtil.isBlank(keyword)) {
            return List.of();
        }
        
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        if (limit > 50) {
            limit = 50;
        }
        
        try {
            Pageable pageable = PageRequest.of(0, limit);
            Page<AddressDocument> searchResult = addressRepository.autocomplete(
                keyword.trim(), pageable);
            
            return searchResult.getContent();
            
        } catch (Exception e) {
            log.error("自动补全搜索失败，关键词: {}, 错误: {}", keyword, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 将数据库实体转换为Elasticsearch文档
     */
    public AddressDocument convertToDocument(TmsBaseCityStreetPostcodeEntity entity) {
        AddressDocument document = new AddressDocument();
        document.setId(String.valueOf(entity.getId()));
        document.setThreePostCode(entity.getThreePostCode());
        document.setSixPostCode(entity.getSixPostCode());
        document.setCity(entity.getCity());
        document.setProvince(entity.getProvince());
        document.setStreet(entity.getRstreet());
        document.setStatus(entity.getStatus());
        
        // 构建完整地址
        StringBuilder fullAddress = new StringBuilder();
        if (StrUtil.isNotBlank(entity.getRstreet())) {
            fullAddress.append(entity.getRstreet()).append(" ");
        }
        if (StrUtil.isNotBlank(entity.getCity())) {
            fullAddress.append(entity.getCity()).append(" ");
        }
        if (StrUtil.isNotBlank(entity.getProvince())) {
            fullAddress.append(entity.getProvince()).append(" ");
        }
        if (StrUtil.isNotBlank(entity.getSixPostCode())) {
            fullAddress.append(entity.getSixPostCode());
        }
        document.setFullAddress(fullAddress.toString().trim());
        
        // 构建搜索建议字段
        StringBuilder suggest = new StringBuilder();
        if (StrUtil.isNotBlank(entity.getThreePostCode())) {
            suggest.append(entity.getThreePostCode()).append(" ");
        }
        if (StrUtil.isNotBlank(entity.getSixPostCode())) {
            suggest.append(entity.getSixPostCode()).append(" ");
        }
        if (StrUtil.isNotBlank(entity.getCity())) {
            suggest.append(entity.getCity()).append(" ");
        }
        if (StrUtil.isNotBlank(entity.getRstreet())) {
            suggest.append(entity.getRstreet()).append(" ");
        }
        document.setSuggest(suggest.toString().trim());
        
        return document;
    }

    /**
     * 批量保存文档到Elasticsearch
     */
    public void saveDocuments(List<AddressDocument> documents) {
        try {
            addressRepository.saveAll(documents);
            log.info("成功保存{}条地址文档到Elasticsearch", documents.size());
        } catch (Exception e) {
            log.error("保存文档到Elasticsearch失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 删除所有文档
     */
    public void deleteAllDocuments() {
        try {
            addressRepository.deleteAll();
            log.info("成功删除所有地址文档");
        } catch (Exception e) {
            log.error("删除文档失败: {}", e.getMessage(), e);
        }
    }
}
