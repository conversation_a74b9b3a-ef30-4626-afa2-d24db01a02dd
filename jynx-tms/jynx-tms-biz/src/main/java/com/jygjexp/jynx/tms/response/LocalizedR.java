package com.jygjexp.jynx.tms.response;

import com.jygjexp.jynx.common.core.constant.CommonConstants;
import com.jygjexp.jynx.common.core.util.R;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * @Author: chenchang
 * @Description: 自定义响应体，用于国际化
 * @Date: 2024/12/5 17:14
 */
public class LocalizedR<T> extends R<T> {

    // 静态注入 MessageSource，用于国际化
    private static MessageSource messageSource;

    public static void setMessageSource(MessageSource source) {
        messageSource = source;
    }

    // 使用国际化消息的 failed 方法
    public static LocalizedR<Object> failed(String messageKey, Object... args) {
        String localizedMessage = getMessage(messageKey, args);
        return (LocalizedR<Object>) new LocalizedR<Object>().setCode(CommonConstants.FAIL).setMsg(localizedMessage);
    }

    public static LocalizedR<Object> failed(String messageKey, String args) {
        String localizedMessage = getMessage(messageKey, new Object[]{args});
        return (LocalizedR<Object>) new LocalizedR<Object>().setCode(CommonConstants.FAIL).setMsg(localizedMessage);
    }

    // 重写 ok 方法（支持国际化消息）
    public static LocalizedR<Object> ok(String messageKey, Object... args) {
//        String localizedMessage = messageSource.getMessage(messageKey, args, LocaleContextHolder.getLocale());
        String localizedMessage = getMessage(messageKey, new Object[]{args});
        return (LocalizedR<Object>) new LocalizedR<Object>().setCode(CommonConstants.SUCCESS).setMsg(localizedMessage);
    }

    public static LocalizedR<Object> ok(String messageKey, String args) {
        String localizedMessage = getMessage(messageKey, new Object[]{args});
        return (LocalizedR<Object>) new LocalizedR<Object>().setCode(CommonConstants.SUCCESS).setMsg(localizedMessage);
    }

    public static String getMessage(String code, Object[] args) {
        // 从 MessageSource 获取多语言消息
        Locale locale = LocaleContextHolder.getLocale();
        return messageSource.getMessage(code, args, locale);
    }

}
