package com.jygjexp.jynx.tms.document;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Setting;
import lombok.Data;

/**
 * 地址Elasticsearch文档
 *
 * <AUTHOR>
 * @date 2025-09-02
 */
@Data
@Document(indexName = "address_autocomplete")   //声明 ES 索引文档类
@Setting(settingPath = "elasticsearch/address-settings.json") //指定索引的配置
public class AddressDocument {

    @Id
    private String id;

    /**
     * 三字邮编 - 支持前缀匹配
     */
    @Field(type = FieldType.Keyword)    //字段在 ES 中的类型
    private String threePostCode;

    /**
     * 六字邮编 - 支持前缀匹配
     */
    @Field(type = FieldType.Keyword)
    private String sixPostCode;

    /**
     * 城市 - 支持分词搜索
     */
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String city;

    /**
     * 省州 - 支持分词搜索
     */
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String province;

    /**
     * 街道 - 支持分词搜索
     */
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String street;

    /**
     * 完整地址 - 用于全文搜索
     */
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String fullAddress;

    /**
     * 搜索建议字段 - 用于自动补全
     */
    @Field(type = FieldType.Search_As_You_Type)
    private String suggest;

    /**
     * 状态
     */
    @Field(type = FieldType.Integer)
    private Integer status;
}
