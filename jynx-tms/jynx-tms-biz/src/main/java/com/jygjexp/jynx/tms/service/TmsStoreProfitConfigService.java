package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.extension.mapping.base.MPJDeepService;
import com.jygjexp.jynx.tms.dto.TmsStoreProfitConfigDTO;
import com.jygjexp.jynx.tms.entity.TmsStoreProfitConfigDetailEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreProfitConfigEntity;
import com.jygjexp.jynx.tms.vo.TmsStoreProfitConfigPageVo;

import java.util.List;
import java.util.Map;

public interface TmsStoreProfitConfigService extends MPJDeepService<TmsStoreProfitConfigEntity> {

    Page<TmsStoreProfitConfigEntity> search(Page page, TmsStoreProfitConfigPageVo vo);

    Boolean saveDeep(TmsStoreProfitConfigEntity tmsStoreProfitConfig);

    Boolean updateDeep(TmsStoreProfitConfigEntity tmsStoreProfitConfig);

    Boolean removeDeep(Long[] ids);

    Boolean removeChild(Long[] ids);

    TmsStoreProfitConfigDTO getByIdDeatil(Long id);
    /**
     * 启用或停用主表配置
     * @param id 主表ID
     * @param isValid 启用/停用标识 1启用 0停用
     */
    Boolean updateStatus(Long id, Integer isValid);

    /**
     * 查询服务商利润配置Map-基于是否仅用于盲盒开关
     * key: providerId
     * value: TmsStoreProfitConfigEntity
     */
    Map<Long,TmsStoreProfitConfigEntity> getMapByProviderIdsAndBoxFlag(List<Long> providerIds, Integer blindBoxFlag);
}
