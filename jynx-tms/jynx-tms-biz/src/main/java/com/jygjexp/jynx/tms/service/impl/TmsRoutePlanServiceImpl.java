package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.*;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.enums.EntrustedOrderStatus;
import com.jygjexp.jynx.tms.enums.ShipmentOrderEnum;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.TmsDriverMapper;
import com.jygjexp.jynx.tms.mapper.TmsRoutePlanMapper;
import com.jygjexp.jynx.tms.mapper.TmsVehicleDriverRelationMapper;
import com.jygjexp.jynx.tms.mongo.entity.TmsRealTimeLocation;
import com.jygjexp.jynx.tms.mongo.service.TmsRealTimeLocationService;
import com.jygjexp.jynx.tms.request.OptimizationRequest;
import com.jygjexp.jynx.tms.request.builder.RequestBuilder;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.vo.ActualRouteInfoVo;
import com.jygjexp.jynx.tms.vo.GeocodeResult;
import com.jygjexp.jynx.tms.vo.RoutePointVo;
import com.jygjexp.jynx.tms.vo.VehicleLocaltionPointVo;
import com.mongoplus.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URLEncoder;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 路线规划主表
 *
 * <AUTHOR>
 * @date 2025-03-17 15:31:15
 */
@Service
@AllArgsConstructor
@Slf4j
public class TmsRoutePlanServiceImpl extends ServiceImpl<TmsRoutePlanMapper, TmsRoutePlanEntity> implements TmsRoutePlanService {

    private final TmsShipmentOrderService tmsShipmentOrderService;
    private final TmsVehicleInfoService tmsVehicleInfoService;
    private final TmsDriverMapper tmsDriverMapper;
    private final TmsEntrustedOrderService tmsEntrustedOrderService;
    private final TmsVehicleRouteService tmsVehicleRouteService;
    private final TmsVehicleRouteVisitService tmsVehicleRouteVisitService;
    private final TmsVehicleRouteTransitionService tmsVehicleRouteTransitionService;
    private final TmsCarrierService tmsCarrierService;
    private final TmsRealTimeLocationService tmsRealTimeLocationService;
    private final TmsVehicleDriverRelationMapper vehicleDriverRelationMapper;

    @Value("${spring.profiles.active:}")
    private String activeProfile;
    private final TmsRoutePlanFailRecordService tmsRoutePlanFailRecordService;
    @Override
    @Transactional
    public R saveRoutePlan(TmsRoutePlanDto tmsRoutePlanDto) {
        TmsShipmentOrderEntity tmsShipmentOrder = tmsShipmentOrderService.getOne(new LambdaQueryWrapper<TmsShipmentOrderEntity>()
                .eq(TmsShipmentOrderEntity::getShipmentNo, tmsRoutePlanDto.getShipmentNo()));
        if(ObjectUtil.isNotNull(tmsShipmentOrder)){
            if(tmsShipmentOrder.getShipmentStatus().equals(ShipmentOrderEnum.COMPLETED.getType())){
                throw new CustomBusinessException(412, LocalizedR.getMessage("route_planning.shipment_completed", null));
            }
        }else{
            throw new CustomBusinessException(412, LocalizedR.getMessage("route_planning.shipment_not_found", null));
        }
        //判断该运输单是否规划过路线
        List<TmsVehicleRouteEntity> tmsVehicleRouteEntityList = tmsVehicleRouteService.list(new LambdaQueryWrapper<TmsVehicleRouteEntity>()
                .eq(TmsVehicleRouteEntity::getShipmentNo, tmsRoutePlanDto.getShipmentNo()));
        if(!tmsVehicleRouteEntityList.isEmpty()){
            throw new CustomBusinessException(6,LocalizedR.getMessage("route_planning.route_already_planned",null));
        }
        //处理委托单数据(只拿主委托单进行路径规划即可，因为子单的地址是跟主单一样的，只是拆分了而已)
        List<TmsEntrustedOrderEntity> tmsEntrustedOrderEntitys = tmsEntrustedOrderService.list(new LambdaQueryWrapper<TmsEntrustedOrderEntity>()
                .eq(TmsEntrustedOrderEntity::getShipmentNo, tmsRoutePlanDto.getShipmentNo())
                .eq(TmsEntrustedOrderEntity::getIsSubOrderNo, Boolean.FALSE)).stream().collect(Collectors.toList());
        //收集（主）委托单的始发地与目的地的经纬度信息
        Map<String, String> orderNumberLatLngMap = new HashMap<>();
        for (TmsEntrustedOrderEntity tmsEntrustedOrderEntity : tmsEntrustedOrderEntitys) {
            String entrustedOrderNumber = tmsEntrustedOrderEntity.getEntrustedOrderNumber();
            String shipperAddress = tmsEntrustedOrderEntity.getShipperLatLng();
            String receiverLatLng = tmsEntrustedOrderEntity.getReceiverLatLng();
            orderNumberLatLngMap.put(entrustedOrderNumber, shipperAddress + "/" + receiverLatLng);
        }
        Map<String, TmsEntrustedOrderEntity> entrustedOrderNumberMap = tmsEntrustedOrderEntitys.stream()
                .collect(Collectors.toMap(TmsEntrustedOrderEntity::getEntrustedOrderNumber, Function.identity()));
        //处理司机车辆信息
        if(ObjectUtil.isNull(tmsRoutePlanDto.getDriverId())){
            return R.failed(LocalizedR.getMessage("route_planning.driver_not_found",null));
        }
        TmsVehicleDriverRelationEntity tmsVehicleDriverRelation = vehicleDriverRelationMapper.selectOne(new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                .eq(TmsVehicleDriverRelationEntity::getDriverId, tmsRoutePlanDto.getDriverId()), false);
        TmsVehicleInfoEntity vehicleInfo= tmsVehicleInfoService.getOne(new LambdaQueryWrapper<TmsVehicleInfoEntity>()
                .eq(TmsVehicleInfoEntity::getId, tmsVehicleDriverRelation.getVehicleId()));
        if(ObjectUtil.isNull(vehicleInfo)){
            throw  new CustomBusinessException(412,LocalizedR.getMessage("route_planning.vehicle_not_found",null));
        }


        // 构造 HTTP 客户端
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();
        //判断当前环境，测试用
        String osName = System.getProperty("os.name").toLowerCase();
        // 如果是 dev 环境，设置代理
        if ("dev".equals(activeProfile)|(osName.contains("win"))) {
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 7890));
            clientBuilder.proxy(proxy);
        }

        OkHttpClient client = clientBuilder.build();

        //先获取谷歌地图授权token令牌
        String finalToken = getToken();
        if(StrUtil.isEmpty(finalToken)){
            return R.failed(LocalizedR.getMessage("route_planning.token_obtain_failed",null));
        }
        MediaType mediaType = MediaType.parse("application/json");

        //利用建造者模式构建请求参数对象
        String jsonString = buildRequest(tmsRoutePlanDto, orderNumberLatLngMap, entrustedOrderNumberMap, Arrays.asList(vehicleInfo));

        RequestBody body = RequestBody.create(jsonString, mediaType);

        Request request = new Request.Builder()
                .url("https://routeoptimization.googleapis.com/v1/projects/nbexpress-433910:optimizeTours")
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", finalToken)
                .addHeader("Connection", "keep-alive")
                .build();
        // 发送请求并处理响应
        String responseBody="";
        try (Response response = client.newCall(request).execute()) {
            if(response.code()==401){
                return R.failed(LocalizedR.getMessage("route_planning.network_fluctuation",null));
            }
            if(response.code()==400){
                return R.failed(LocalizedR.getMessage("route_planning.invalid_input_parameters",null));
            }
            if (response.isSuccessful() && response.body() != null) {
                // 解析响应结果，保存路线规划信息
                parseAndSaveResponse(response, orderNumberLatLngMap,tmsRoutePlanDto.getShipmentNo(),tmsRoutePlanDto.getDriverId(),1);
            } else {
                log.error("谷歌地图路线规划请求失败------------------>: " + response.code()+response.body().string());
            }
        } catch (IOException e) {
            e.printStackTrace();
           return R.failed(LocalizedR.getMessage("route_planning.request_failed",null));
        }
        return R.ok(LocalizedR.getMessage("route_planning.success",null));
    }

    /**
     * 初次构建路径规划请求参数（卡派）
     */
    @NotNull
    private String buildRequest(TmsRoutePlanDto tmsRoutePlanDto, Map<String, String> orderNumberLatLngMap, Map<String, TmsEntrustedOrderEntity> entrustedOrderNumberMap, List<TmsVehicleInfoEntity> vehicleInfos) {
        OptimizationRequest.Model model = new OptimizationRequest.Model();
        RequestBuilder builder = new RequestBuilder(model,true,true);
        //设置此次路线规划的全局时间限制范围（当前时间往前后波动2天，避免时区不同导致超出了全局时间范围）
        LocalDate now = LocalDate.now();
        LocalDateTime globalStartTime = LocalDateTime.of(now.minusDays(2), LocalTime.of(0, 0, 0));
        LocalDateTime globalEndTime = LocalDateTime.of(now.plusDays(2), LocalTime.of(23, 59, 59));
        //均使用UTC时间格式（谷歌地图要求UTC格式的时间）
        String globalStartTimeUTCString = globalStartTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
        String globalEndTimeUTCString = globalEndTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
//        builder.withGlobalTime("2025-03-19T07:00:00Z", "2025-03-20T06:59:00Z");
        builder.withGlobalTime(globalStartTimeUTCString, globalEndTimeUTCString);
        //根据委托单个数设置派送任务Shipments
        orderNumberLatLngMap.keySet().forEach(entrustedOrderNumber -> {
            //处理从委托单里拿出来的(取货送货点)经纬度
            String latAndLngString = orderNumberLatLngMap.get(entrustedOrderNumber);
            TmsEntrustedOrderEntity tmsEntrustedOrderEntity = entrustedOrderNumberMap.get(entrustedOrderNumber);
            //取货送货点经纬度字符串（24.33211,114.54322/24.44431,114.84322）
            String[] split = latAndLngString.split("/");
            //取货点经纬度字符串（24.33211,114.54322）
            String pickupLatLng = split[0];
            String[] pickupLatLngArr = pickupLatLng.split(",");
            //送货点经纬度字符串（24.44431,114.84322）
            String deliveryLatLng = split[1];
            String[] deliveryLatLngArr = deliveryLatLng.split(",");
            //既然安排到今天，则不管你的派送日期是什么都取今天的日期，时间则取客户下单时定的时间段，保证路线规划不会因日期范围而路径规划失败的情况
            //由于一加时间条件就路径规划失败，所以这里先把时间条件去掉（暂留）
            LocalDate startDate = LocalDate.now();
            LocalDate endDate = LocalDate.now();
            LocalDateTime pickupStartDateTime = LocalDateTime.of(startDate, tmsEntrustedOrderEntity.getEstimatedShippingTimeStart().toLocalTime());
            LocalDateTime pickupendDateTime = LocalDateTime.of(endDate, tmsEntrustedOrderEntity.getEstimatedShippingTimeEnd().toLocalTime());
            LocalDateTime deliveryStartDateTime = LocalDateTime.of(startDate, tmsEntrustedOrderEntity.getEstimatedArrivalTimeStart().toLocalTime());
            LocalDateTime deliveryEndDateTime = LocalDateTime.of(endDate, tmsEntrustedOrderEntity.getEstimatedArrivalTimeEnd().toLocalTime());
            //根据委托单构建shipment运输任务对象（请求参数）
            builder .addShipment(shipment -> shipment
                    //取货点条件参数
                    .withPickup(pickup -> pickup
                            //取货点的经纬度
                            .withLocation(new BigDecimal(pickupLatLngArr[0]), new BigDecimal(pickupLatLngArr[1]))
                            //取货点所需大概时间（因没有限定，这里暂用10分钟）
                            .withDuration("600s")
//                            .addTimeWindow(pickupStartDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT), pickupendDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT))
                    )
                    //送货点条件参数
                    .withDelivery(delivery -> delivery
                            //送货点的经纬度
                            .withLocation(new BigDecimal(deliveryLatLngArr[0]), new BigDecimal(deliveryLatLngArr[1]))
                            //送货点所需大概时间（因没有限定，这里也暂用10分钟）
                            .withDuration("600s")
//                            .addTimeWindow(deliveryStartDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT), deliveryEndDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT))
                    )
                    //此运输任务的载重
                    .withLoadDemand(tmsEntrustedOrderEntity.getTotalWeight().intValue())
                    //委托单号作为label唯一标记
                    .withLabel(entrustedOrderNumber)
            );
        });
        //设置车辆（司机）Vehicles
        Map<Long, TmsCarrierEntity> carrierMap=new HashMap<>();
        //承运商信息
        List<Long> carrierIds = vehicleInfos.stream().map(TmsVehicleInfoEntity::getCarrierId).collect(Collectors.toList());
        if(ObjectUtil.isNotNull(carrierIds) && !carrierIds.isEmpty()){
            carrierMap  = tmsCarrierService.list(new LambdaQueryWrapper<TmsCarrierEntity>()
                    .in(TmsCarrierEntity::getCarrierId, carrierIds)).stream()
                    .collect(Collectors.toMap(TmsCarrierEntity::getCarrierId,Function.identity()));
        }
        //预留多个司机的情况（预留后期扩展）
        for (TmsVehicleInfoEntity vehicleInfo : vehicleInfos) {
            TmsCarrierEntity carrierEntity=null;
            if(ObjectUtil.isNotNull(carrierMap.get(vehicleInfo.getCarrierId()))){
                carrierEntity = carrierMap.get(vehicleInfo.getCarrierId());
            }
            String vehicleWorkStartTime;
            String vehicleWorkEndTime;
            if(ObjectUtil.isNotNull(carrierEntity) && ObjectUtil.isNotNull(carrierEntity.getOpeningTime()) && ObjectUtil.isNotNull(carrierEntity.getClosingTime())){
                //统统取今天的时间（日期取今天，时间段取司机工作时间）
                LocalDate startDate = LocalDate.now();
                LocalDate endDate = LocalDate.now();
                LocalDateTime startDateTime = LocalDateTime.of(startDate, carrierEntity.getOpeningTime());
                LocalDateTime endDateTime = LocalDateTime.of(endDate, carrierEntity.getClosingTime());
                vehicleWorkStartTime = startDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
                vehicleWorkEndTime = endDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
            } else {
                vehicleWorkEndTime = null;
                vehicleWorkStartTime = null;
            }
            //构建谷歌路线规划的司机车辆信息（请求参数）
            builder .addVehicle(vehicle -> vehicle
                    .withStartLocation(tmsRoutePlanDto.getDriverLat(), tmsRoutePlanDto.getDriverLng())
                    //应需求，先暂时无限大（1000吨---货物是kg，车辆10000吨，相当于不加载重参数，预留，防止后期扩展需要）
                    .withLoadLimit(new BigDecimal(10000).multiply(new BigDecimal(1000)).intValue())
                    //todo 后期加上司机的时间窗口限制
//                    .withTimeWindows(vehicleWorkStartTime, vehicleWorkEndTime)
                    //车辆司机id作为label唯一标记
                    .withLabel(vehicleInfo.getDriverId().toString())
                    //每公里成本(由于车辆信息没有所以先暂时固定)
                    .withCostPerKilometer(6)
            );
        }
        //利用build模式创建谷歌路线规划请求参数
        OptimizationRequest optimizationRequest = builder.build();

        return JSON.toJSONString(optimizationRequest);
    }

    /**
     * 中途路径规划构建请求参数
     */
    @NotNull
    private String replanningBuildRequest(TmsRouteReplanningDto tmsRouteReplanningDto, Map<String, String> orderNumberLatLngMap, Map<String, TmsEntrustedOrderEntity> entrustedOrderNumberMap, List<TmsVehicleInfoEntity> vehicleInfos) {
        OptimizationRequest.Model model = new OptimizationRequest.Model();
        RequestBuilder builder = new RequestBuilder(model,true,true);
        //设置此次路线规划的全局时间限制范围（当前时间往前后波动2天，避免时区不同导致超出了全局时间范围）
        LocalDate now = LocalDate.now();
        LocalDateTime globalStartTime = LocalDateTime.of(now.minusDays(2), LocalTime.of(0, 0, 0));
        LocalDateTime globalEndTime = LocalDateTime.of(now.plusDays(2), LocalTime.of(23, 59, 59));
        //均转为谷歌地图需要的UTC时间格式
        String globalStartTimeUTCString = globalStartTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
        String globalEndTimeUTCString = globalEndTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
//        builder.withGlobalTime("2025-03-19T07:00:00Z", "2025-03-20T06:59:00Z");
        builder.withGlobalTime(globalStartTimeUTCString, globalEndTimeUTCString);
        //设置派送任务Shipments
        orderNumberLatLngMap.keySet().forEach(entrustedOrderNumber -> {
            String latAndLngString = orderNumberLatLngMap.get(entrustedOrderNumber);
            TmsEntrustedOrderEntity tmsEntrustedOrderEntity = entrustedOrderNumberMap.get(entrustedOrderNumber);
            String[] split = latAndLngString.split("/");
            String pickupLatLng = split[0];
            String[] pickupLatLngArr = pickupLatLng.split(",");
            String deliveryLatLng = split[1];
            String[] deliveryLatLngArr = deliveryLatLng.split(",");
            //既然安排到今天，则不管你的派送日期是什么都取今天的日期，时间则取客户下单时定的时间段，保证路线规划不会因日期范围而路径规划失败的情况
            LocalDate startDate = LocalDate.now();
            LocalDate endDate = LocalDate.now();
            LocalDateTime pickupStartDateTime = LocalDateTime.of(startDate, tmsEntrustedOrderEntity.getEstimatedShippingTimeStart().toLocalTime());
            LocalDateTime pickupEndDateTime = LocalDateTime.of(endDate, tmsEntrustedOrderEntity.getEstimatedShippingTimeEnd().toLocalTime());
            LocalDateTime deliveryStartDateTime = LocalDateTime.of(startDate, tmsEntrustedOrderEntity.getEstimatedArrivalTimeStart().toLocalTime());
            LocalDateTime deliveryEndDateTime = LocalDateTime.of(endDate, tmsEntrustedOrderEntity.getEstimatedArrivalTimeEnd().toLocalTime());
            //此时的委托单状态是待提货，则请求参数需要包含提货点和派送点的经纬度
            if(tmsEntrustedOrderEntity.getOrderStatus().equals(EntrustedOrderStatus.PENDING_PICKUP.getCode())){
                builder .addShipment(shipment -> shipment
                        .withPickup(pickup -> pickup
                                .withLocation(new BigDecimal(pickupLatLngArr[0]), new BigDecimal(pickupLatLngArr[1]))
                                .withDuration("600s")
//                                .addTimeWindow(pickupStartDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT), pickupEndDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT))
                        )
                        .withDelivery(delivery -> delivery
                                .withLocation(new BigDecimal(deliveryLatLngArr[0]), new BigDecimal(deliveryLatLngArr[1]))
                                .withDuration("600s")
//                                .addTimeWindow(deliveryStartDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT), deliveryEndDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT))
                        )
                        .withLoadDemand(tmsEntrustedOrderEntity.getTotalWeight().intValue())
                        //委托单号作为label唯一标记
                        .withLabel(entrustedOrderNumber)
                );
            }else{
                //此时的委托单状态是运输中，则请求参数只需要派送点的经纬度不需要包含提货点经纬度
                builder .addShipment(shipment -> shipment
                        .withDelivery(delivery -> delivery
                                .withLocation(new BigDecimal(deliveryLatLngArr[0]), new BigDecimal(deliveryLatLngArr[1]))
                                .withDuration("600s")
//                                .addTimeWindow(deliveryStartDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT), deliveryEndDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT))
                        )
                        .withLoadDemand(tmsEntrustedOrderEntity.getTotalWeight().intValue())
                        //委托单号作为label唯一标记
                        .withLabel(entrustedOrderNumber)
                );
            }

        });
        //设置车辆（司机）Vehicles
        Map<Long, TmsCarrierEntity> carrierMap=new HashMap<>();
        List<Long> carrierIds = vehicleInfos.stream().map(TmsVehicleInfoEntity::getCarrierId).collect(Collectors.toList());
        if(ObjectUtil.isNotNull(carrierIds) && !carrierIds.isEmpty()){
            carrierMap  = tmsCarrierService.list(new LambdaQueryWrapper<TmsCarrierEntity>().in(TmsCarrierEntity::getCarrierId, carrierIds)).stream().collect(Collectors.toMap(TmsCarrierEntity::getCarrierId,Function.identity()));
        }
        for (TmsVehicleInfoEntity vehicleInfo : vehicleInfos) {
            TmsCarrierEntity carrierEntity=null;
            if(ObjectUtil.isNotNull(carrierMap.get(vehicleInfo.getCarrierId()))){
                carrierEntity = carrierMap.get(vehicleInfo.getCarrierId());
            }
            String vehicleWorkStartTime;
            String vehicleWorkEndTime;
            if(ObjectUtil.isNotNull(carrierEntity) && ObjectUtil.isNotNull(carrierEntity.getOpeningTime()) && ObjectUtil.isNotNull(carrierEntity.getClosingTime())){
                //统统取今天的时间（日期取今天，时间段取司机工作时间）
                LocalDate startDate = LocalDate.now();
                LocalDate endDate = LocalDate.now();
                LocalDateTime startDateTime = LocalDateTime.of(startDate, carrierEntity.getOpeningTime());
                LocalDateTime endDateTime = LocalDateTime.of(endDate, carrierEntity.getClosingTime());
                vehicleWorkStartTime = startDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
                vehicleWorkEndTime = endDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
            } else {
                vehicleWorkEndTime = null;
                vehicleWorkStartTime = null;
            }
            builder .addVehicle(vehicle -> vehicle
                            .withStartLocation(tmsRouteReplanningDto.getDriverLat(), tmsRouteReplanningDto.getDriverLng())
                            //应需求，先暂时无限大（1000吨---货物是kg，车辆10000吨）
                            .withLoadLimit(new BigDecimal(10000).multiply(new BigDecimal(1000)).intValue())
                            //todo 后期加上司机的时间窗口限制
//                            .withTimeWindows(vehicleWorkStartTime, vehicleWorkEndTime)
                            //车辆司机id作为label唯一标记
                            .withLabel(vehicleInfo.getDriverId().toString())
                            //每公里成本(由于车辆信息没有所以先暂时固定)
                            .withCostPerKilometer(6)
            );
        }
        OptimizationRequest optimizationRequest = builder.build();

        return JSON.toJSONString(optimizationRequest);
    }

    /**
     * 解析响应结果，保存路线规划信息
     */
    private void parseAndSaveResponse(Response response, Map<String, String> orderNumberLatLngMap,String shipmentNo,Long driverId,Integer type) throws IOException {
        String responseBody;
        responseBody = response.body().string();
        JSONObject jsonObject = JSON.parseObject(responseBody);
        //车辆路线信息routes
        JSONArray routes = jsonObject.getJSONArray("routes");
        //全部失败,没有路线的情况
        if(routes.isEmpty() || ObjectUtil.isNull(routes) ){
            JSONArray skippedShipments = jsonObject.getJSONArray("skippedShipments");
            JSONObject skippedShipment = skippedShipments.getJSONObject(0);
            String orderLabel = skippedShipment.getString("label");
            JSONArray reasons = skippedShipment.getJSONArray("reasons");
            JSONObject reason = reasons.getJSONObject(0);
            String code = reason.getString("code");
            throw new CustomBusinessException(500,LocalizedR.getMessage("route_planning.failure_reason", new String[]{code}));
        }
        //部分委托单规划失败的情况
        JSONArray skippedShipments = jsonObject.getJSONArray("skippedShipments");
        List<TmsRoutePlanFailRecordEntity> tmsRoutePlanFailRecordEntities = new ArrayList<>();
        if(ObjectUtil.isNotNull(skippedShipments)){
            if(!skippedShipments.isEmpty()){
                List<String> entrustedOrderNumbers = new ArrayList<>();
                for (int i=0;i<skippedShipments.size();i++) {
                    //将失败的委托单进行记录
                    JSONObject item = skippedShipments.getJSONObject(i);
                    String orderLabel = item.getString("label");
                    JSONArray reasons = item.getJSONArray("reasons");
                    JSONObject reason = reasons.getJSONObject(0);
                    TmsRoutePlanFailRecordEntity tmsRoutePlanFailRecordEntity = new TmsRoutePlanFailRecordEntity();
                    tmsRoutePlanFailRecordEntity.setFailKey(driverId+"");
                    tmsRoutePlanFailRecordEntity.setDate(LocalDate.now());
                    tmsRoutePlanFailRecordEntity.setFailOrder(orderLabel);
                    tmsRoutePlanFailRecordEntity.setFailReason(reason.getString("code"));
                    tmsRoutePlanFailRecordEntity.setStatus(0);
                    if(type.equals(1)){
                        tmsRoutePlanFailRecordEntity.setType("首次路径规划失败");
                    }else{
                        tmsRoutePlanFailRecordEntity.setType("中途路径规划失败");
                    }
                    tmsRoutePlanFailRecordEntities.add(tmsRoutePlanFailRecordEntity);
                    //存储规划失败的委托单号
                    entrustedOrderNumbers.add(orderLabel);
                }
                //保存失败记录
                tmsRoutePlanFailRecordService.saveBatch(tmsRoutePlanFailRecordEntities);
                //规划失败的将其移出当前运输单，放回原来的订单池里重新指派调度（因为一般失败都是因为指派的单的重量超过了车的最大载重）
                List<TmsEntrustedOrderEntity> tmsEntrustedOrderEntities = tmsEntrustedOrderService.list(new LambdaQueryWrapper<TmsEntrustedOrderEntity>().in(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers));
                if(ObjectUtil.isNotNull(tmsEntrustedOrderEntities) && !tmsEntrustedOrderEntities.isEmpty()){
                    for (TmsEntrustedOrderEntity tmsEntrustedOrderEntity : tmsEntrustedOrderEntities) {
                        tmsEntrustedOrderEntity.setShipmentNo(null);
                    }
                    //更新，以便剔除
                    tmsEntrustedOrderService.updateBatchById(tmsEntrustedOrderEntities);
                }
            }
        }
        JSONObject metrics = jsonObject.getJSONObject("metrics");
        JSONObject aggregatedRouteMetrics = metrics.getJSONObject("aggregatedRouteMetrics");
        if(ObjectUtil.isNull(aggregatedRouteMetrics)){
            //到这里空指针异常的话一般就是规划地址范围过大的情况（经纬度）,比如定位起点是国内,但是目的地在加拿大的情况（会失败）
            throw new CustomBusinessException(LocalizedR.getMessage("route_planning.address_range_too_large",null));
        }
        //路径规划成功的shipment个数
        Integer performedShipmentCount = aggregatedRouteMetrics.getInteger("performedShipmentCount");
        //车辆在完成路线时在运输途中所花费的总时间
        String travelDuration = aggregatedRouteMetrics.getString("travelDuration");
        //是车辆在完成路线时等待的总时间
        String waitDuration = aggregatedRouteMetrics.getString("waitDuration");
        //因特殊情况绕道的耗时
        String delayDuration = aggregatedRouteMetrics.getString("delayDuration");
        //路线规划的司机休息时间耗时（如果请求参数中有设置的话，如果没有就是0s）
        String breakDuration = aggregatedRouteMetrics.getString("breakDuration");
        //车辆在完成路线时执行访问所花费的总时间
        String visitDuration = aggregatedRouteMetrics.getString("visitDuration");
        //完成车辆路线所需的总时长(所有累加)
        String totalDuration = aggregatedRouteMetrics.getString("totalDuration");
        //车辆完成路线行驶的总距离
        Integer travelDistanceMeters = aggregatedRouteMetrics.getInteger("travelDistanceMeters");
        //车辆在完成路线时执行访问最大载荷量累加
        JSONObject maxLoads = aggregatedRouteMetrics.getJSONObject("maxLoads");
        JSONObject weight = maxLoads.getJSONObject("weight");
        String amount = weight.getString("amount");
        //用到的司机数
        Integer usedVehicleCount = metrics.getInteger("usedVehicleCount");
        //路线规划的司机最早开始时间
        String earliestVehicleStartTime = metrics.getString("earliestVehicleStartTime");
        //路线规划的司机最晚结束时间
        String latestVehicleEndTime = metrics.getString("latestVehicleEndTime");
        //总共花费
        BigDecimal totalCost = metrics.getBigDecimal("totalCost");
        //花费明细项
        JSONObject costs = metrics.getJSONObject("costs");
        String costsString = JSON.toJSONString(costs);
        //存路线规划主表
        TmsRoutePlanEntity tmsRoutePlanEntity = new TmsRoutePlanEntity();
        tmsRoutePlanEntity.setPerformedShipmentCount(performedShipmentCount);
        tmsRoutePlanEntity.setTravelDuration(travelDuration);
        tmsRoutePlanEntity.setWaitDuration(waitDuration);
        tmsRoutePlanEntity.setDelayDuration(delayDuration);
        tmsRoutePlanEntity.setBreakDuration(breakDuration);
        tmsRoutePlanEntity.setVisitDuration(visitDuration);
        tmsRoutePlanEntity.setTotalDuration(totalDuration);
        tmsRoutePlanEntity.setTravelDistanceMeters(Long.valueOf(travelDistanceMeters));
        tmsRoutePlanEntity.setTotalLoad(new BigDecimal(amount));
        tmsRoutePlanEntity.setUsedVehicleCount(usedVehicleCount);
        //将谷歌地图返回的UTC时间转为本地时间
        tmsRoutePlanEntity.setEarliestVehicleStartTime(ZonedDateTime.parse(earliestVehicleStartTime).withZoneSameInstant( ZoneId.systemDefault()).toLocalDateTime());
        tmsRoutePlanEntity.setLatestVehicleEndTime(ZonedDateTime.parse(latestVehicleEndTime).withZoneSameInstant( ZoneId.systemDefault()).toLocalDateTime());
        tmsRoutePlanEntity.setTotalCost(totalCost);
        tmsRoutePlanEntity.setCosts(costsString);
        tmsRoutePlanEntity.setShipmentNo(shipmentNo);
        if(type.equals(1)){
            //第一次路径规划
            this.save(tmsRoutePlanEntity);
        }else{
            //后续重新路径规划
            TmsRoutePlanEntity routePlan = this.getOne(new LambdaQueryWrapper<TmsRoutePlanEntity>().eq(TmsRoutePlanEntity::getShipmentNo, shipmentNo));
            tmsRoutePlanEntity.setRoutePlanId(routePlan.getRoutePlanId());
            this.updateById(tmsRoutePlanEntity);

        }
        //解析路线信息，保存路线信息
        parseAndSaveRoute(orderNumberLatLngMap, routes, maxLoads, tmsRoutePlanEntity, shipmentNo,type);
    }

    /**
     * 解析路线信息，保存路线信息
     */
    private void parseAndSaveRoute(Map<String, String> orderNumberLatLngMap, JSONArray routes, JSONObject maxLoads, TmsRoutePlanEntity tmsRoutePlanEntity,String shipmentNo,Integer type) {
        for (Object route : routes) {
            JSONObject routeJson = (JSONObject) route;
            //车辆标识(入参时填充的司机id)
            String vehicleLabel = routeJson.getString("vehicleLabel");
            //车辆开始时间
            String vehicleStartTime = routeJson.getString("vehicleStartTime");
            List<TmsVehicleRouteVisitEntity> tmsVehicleRouteVisitEntities = new ArrayList<>();
            List<TmsVehicleRouteTransitionEntity> tmsVehicleRouteTransitionEntities = new ArrayList<>();
            //vehicleStartTime不为空-即路线规划成功时解析路线信息
            if(ObjectUtil.isNotNull(vehicleStartTime)&& !StrUtil.isEmpty(vehicleStartTime) ){
                //路线车辆预估的结束时间
                String vehicleEndTime = routeJson.getString("vehicleEndTime");
                //谷歌地图返回的路线分段串（解析后得到路线经纬度集合）
                JSONObject routePolyline = routeJson.getJSONObject("routePolyline");
                String points = routePolyline.getString("points");
                //该车辆路线的聚合指标信息
                JSONObject vehicleMetrics = routeJson.getJSONObject("metrics");
                //指派给该车辆路线上的运输单数
                Integer vehiclePerformedShipmentCount = vehicleMetrics.getInteger("performedShipmentCount");
                //该路线上的行驶总时间
                String vehicleTravelDuration = vehicleMetrics.getString("travelDuration");
                //该路线上的等待总时间
                String vehicleWaitDuration = vehicleMetrics.getString("waitDuration");
                //该路线上的延迟总时间
                String vehicleDelayDuration = vehicleMetrics.getString("delayDuration");
                //该路线上的休息总时间
                String vehicleBreakDuration = vehicleMetrics.getString("breakDuration");
                //该路线上的访问总时间
                String vehicleVisitDuration = vehicleMetrics.getString("visitDuration");
                //该路线上的总时间(所有时间累加)
                String vehicleTotalDuration = vehicleMetrics.getString("totalDuration");
                //该路线上的行驶总距离
                Integer vehicleTravelDistanceMeters = vehicleMetrics.getInteger("travelDistanceMeters");
                //该路线上的总装载量
                JSONObject vehicleMaxLoads = vehicleMetrics.getJSONObject("maxLoads");
                JSONObject vehicleWeight = maxLoads.getJSONObject("weight");
                String vehicleAmount = vehicleWeight.getString("amount");
                //该路线上的花费明细项
                JSONObject vehicleRouteCosts = routeJson.getJSONObject("routeCosts");
                String vehicleRouteCostsString = JSON.toJSONString(vehicleRouteCosts);
                //该路线上的花费总额
                BigDecimal routeTotalCost = routeJson.getBigDecimal("routeTotalCost");
                //创建路线实体存储路线信息
                TmsVehicleRouteEntity tmsVehicleRouteEntity = new TmsVehicleRouteEntity();
                //设置运输单号
                tmsVehicleRouteEntity.setShipmentNo(shipmentNo);
                //存储此时的车辆的司机id
                tmsVehicleRouteEntity.setVehicleLabel(vehicleLabel);
                //将谷歌地图返回的UTC时间转为本地时间
                tmsVehicleRouteEntity.setVehicleStartTime(ZonedDateTime.parse(vehicleStartTime).withZoneSameInstant( ZoneId.systemDefault()).toLocalDateTime());
                tmsVehicleRouteEntity.setVehicleEndTime(ZonedDateTime.parse(vehicleEndTime).withZoneSameInstant( ZoneId.systemDefault()).toLocalDateTime());
                //路线规划主表id
                tmsVehicleRouteEntity.setRoutePlanId(tmsRoutePlanEntity.getRoutePlanId());
                if(type.equals(1)){
                    tmsVehicleRouteEntity.setRoutePolylinePoints(points);
                }else{
                    //临时存储此时重新路径规划的线路串(下面转存实时路线字符串的时候也转存一下原来的points串)--针对于后续有要重新规划路线的情况
                    tmsVehicleRouteEntity.setTempRoutePolylinePoints(points);
                }
                //设置存储指派给该车辆路线上的运输单数
                tmsVehicleRouteEntity.setPerformedShipmentCount(vehiclePerformedShipmentCount);
                tmsVehicleRouteEntity.setTravelDuration(vehicleTravelDuration);
                tmsVehicleRouteEntity.setWaitDuration(vehicleWaitDuration);
                tmsVehicleRouteEntity.setDelayDuration(vehicleDelayDuration);
                tmsVehicleRouteEntity.setBreakDuration(vehicleBreakDuration);
                tmsVehicleRouteEntity.setVisitDuration(vehicleVisitDuration);
                tmsVehicleRouteEntity.setTotalDuration(vehicleTotalDuration);
                tmsVehicleRouteEntity.setTravelDistanceMeters(Long.valueOf(vehicleTravelDistanceMeters));
                tmsVehicleRouteEntity.setTotalLoad(new BigDecimal(vehicleAmount));
                tmsVehicleRouteEntity.setRouteCosts(vehicleRouteCostsString);
                tmsVehicleRouteEntity.setRouteTotalCosts(routeTotalCost);
                if(type.equals(1)){
                    //第一次路径规划
                    tmsVehicleRouteEntity.setReplanningSign(0);
                    tmsVehicleRouteService.save(tmsVehicleRouteEntity);
                }else{
                    //后续重新路径规划
                    TmsVehicleRouteEntity vehicleRouteEntity = tmsVehicleRouteService.getOne(new LambdaQueryWrapper<TmsVehicleRouteEntity>().eq(TmsVehicleRouteEntity::getShipmentNo, shipmentNo));
                    tmsVehicleRouteEntity.setReplanningSign(vehicleRouteEntity.getReplanningSign()+1);
                    //路线字符串取原来的实时路线字符串（用来后期的轨迹回放）
                    tmsVehicleRouteEntity.setRouteString(vehicleRouteEntity.getRouteString());
                    //路线串取原来的临时路线串,避免更新丢失原来的路线串
                    tmsVehicleRouteEntity.setRoutePolylinePoints(tmsVehicleRouteEntity.getRoutePolylinePoints());
                    tmsVehicleRouteService.updateById(tmsVehicleRouteEntity);
                }
                //提取访问点信息（有顺序的访问点信息）
                JSONArray visits = routeJson.getJSONArray("visits");
                JSONArray transitions = routeJson.getJSONArray("transitions");
                //访问点顺序记录
                int visitIndex = 0;
                //访问点顺序处理
                for (Object visit : visits) {
                    JSONObject visitJsonObject = (JSONObject) visit;
                    //请求参数时的任务索引（请求委托单的顺序编号）
                    Integer shipmentIndex = visitJsonObject.getInteger("shipmentIndex");
                    int finalIsPickup = 0;
                    //是否提货点
                    Boolean isPickup = visitJsonObject.getBoolean("isPickup");
                    //谷歌返回的预计到达该点的时间
                    String startTime = visitJsonObject.getString("startTime");
                    //谷歌返回的预计配送时间
                    String detour = visitJsonObject.getString("detour");
                    //shipmentLabel：入参时的委托单主号（表示当前访问点是那个委托单）
                    String shipmentLabel = visitJsonObject.getString("shipmentLabel");
                    //委托单中重量
                    JSONObject loadDemands = visitJsonObject.getJSONObject("loadDemands");
                    JSONObject visitWeight = loadDemands.getJSONObject("weight");
                    String shipmentAmount = visitWeight.getString("amount");
                    BigDecimal latitude=BigDecimal.ZERO;
                    BigDecimal longitude=BigDecimal.ZERO;
                    //提货点
                    if(ObjectUtil.isNotNull(isPickup)){
                        //取货点的经纬度处理（根据请求入参的委托单的单号得到该单的取货点的经纬度,存储使用）
                        //取货点标识
                        finalIsPickup=1;
                        //该单的取货和送货点的经纬度（22.34654,114.54324/22.32134,114.67544）
                        String latAndLng = orderNumberLatLngMap.get(shipmentLabel);
                        String[] split = latAndLng.split("/");
                        String finalLatAndLng = split[0];
                        String[] latAndLngSplit = finalLatAndLng.split(",");
                        latitude=new BigDecimal(latAndLngSplit[0]);
                        longitude=new BigDecimal(latAndLngSplit[1]);
                    }else{
                        //送货点的经纬度处理（根据请求入参的委托单的单号得到该单的送货点的经纬度,存储使用）
                        //该单的取货和送货点的经纬度（22.34654,114.54324/22.32134,114.67544）
                        String latAndLng = orderNumberLatLngMap.get(shipmentLabel);
                        String[] split = latAndLng.split("/");
                        String finalLatAndLng = split[1];
                        String[] latAndLngSplit = finalLatAndLng.split(",");
                        latitude=new BigDecimal(latAndLngSplit[0]);
                        longitude=new BigDecimal(latAndLngSplit[1]);
                    }
                    TmsVehicleRouteVisitEntity tmsVehicleRouteVisitEntity = new TmsVehicleRouteVisitEntity();
                    tmsVehicleRouteVisitEntity.setVehicleRouteId(tmsVehicleRouteEntity.getVehicleRouteId());
                    //设置单的访问顺序（参数访问点顺序）
                    tmsVehicleRouteVisitEntity.setOrderNum(++visitIndex);
                    tmsVehicleRouteVisitEntity.setShipmentIndex(shipmentIndex);
                    //存储是否取货点（1：取货点，0：送货点）
                    tmsVehicleRouteVisitEntity.setIsPickup(finalIsPickup);
                    //预计到达该点的时间
                    tmsVehicleRouteVisitEntity.setArrivalTime(ZonedDateTime.parse(startTime).toLocalDateTime());
                    tmsVehicleRouteVisitEntity.setDetour(detour);
                    //委托单标签（委托主单号-哪个单的）
                    tmsVehicleRouteVisitEntity.setShipmentLabel(shipmentLabel);
                    tmsVehicleRouteVisitEntity.setLoadWeight(new BigDecimal(shipmentAmount));
                    //存储访问点的经纬度
                    tmsVehicleRouteVisitEntity.setLatitude(latitude);
                    tmsVehicleRouteVisitEntity.setLongitude(longitude);
                    //是否重新规划区分标志（0：false，1和其他：true）
                    tmsVehicleRouteVisitEntity.setReplanningSign(tmsVehicleRouteEntity.getReplanningSign());
                    tmsVehicleRouteVisitEntities.add(tmsVehicleRouteVisitEntity);
                }
                //过渡段信息处理（两点之间的数据信息）
                int transitionIndex = 0;
                for (Object transition : transitions) {
                    //处理过渡段信息（存储两点间的过渡段信息数据：司机起点->transitions[0]->visits[0]->transitions[1]->visits[1].....组成路线）
                    JSONObject transitionJsonObject = (JSONObject) transition;
                    //两点间车辆行驶时间
                    String transitionTravelDuration = transitionJsonObject.getString("travelDuration");
                    //两点间等待时间
                    String transitionWaitDuration = transitionJsonObject.getString("waitDuration");
                    //两点间行驶距离
                    Integer transitionTravelDistanceMeters = transitionJsonObject.getInteger("travelDistanceMeters");
                    //两点间总时间
                    String transitionTotalDuration = transitionJsonObject.getString("totalDuration");
                    String transitionPolyline=null;
                    String routeToken=null;
                    //过渡段路线字符串（两点间的路线加密串,如果不为空则存储起来-两点间太近、访问点、最后一个是没有的）
                    JSONObject transitionRoutePolyline = transitionJsonObject.getJSONObject("routePolyline");
                    if(ObjectUtil.isNotNull(transitionRoutePolyline)){
                        //路线加密字符串
                        transitionPolyline = transitionRoutePolyline.getString("points");
                        //路线令牌token
                        routeToken = transitionJsonObject.getString("routeToken");
                    }
                    //两点间到达时间
                    String transitionStartTime = transitionJsonObject.getString("startTime");
                    //载重
                    JSONObject transitionVehicleLoads = transitionJsonObject.getJSONObject("vehicleLoads");
                    JSONObject transitionVehicleLoadsWeight = transitionVehicleLoads.getJSONObject("weight");
                    String transitionAmount="";
                    if(ObjectUtil.isNotNull(transitionVehicleLoadsWeight)){
                        transitionAmount = transitionVehicleLoadsWeight.getString("amount");
                    }
                    //存储
                    TmsVehicleRouteTransitionEntity tmsVehicleRouteTransitionEntity = new TmsVehicleRouteTransitionEntity();
                    tmsVehicleRouteTransitionEntity.setVehicleRouteId(tmsVehicleRouteEntity.getVehicleRouteId());
                    tmsVehicleRouteTransitionEntity.setOrderNum(++transitionIndex);
                    tmsVehicleRouteTransitionEntity.setTravelDuration(transitionTravelDuration);
                    tmsVehicleRouteTransitionEntity.setWaitDuration(transitionWaitDuration);
                    if(ObjectUtil.isNotNull(transitionTravelDistanceMeters)){
                        tmsVehicleRouteTransitionEntity.setTravelDistanceMeters(Long.valueOf(transitionTravelDistanceMeters));
                    }
                    tmsVehicleRouteTransitionEntity.setTotalDuration(transitionTotalDuration);
                    tmsVehicleRouteTransitionEntity.setStartTime(ZonedDateTime.parse(transitionStartTime).toLocalDateTime());
                    if(!StrUtil.isEmpty(transitionAmount)){
                        tmsVehicleRouteTransitionEntity.setVehicleLoadsWeight(new BigDecimal(transitionAmount));
                    }else {
                        tmsVehicleRouteTransitionEntity.setVehicleLoadsWeight(BigDecimal.ZERO);
                    }
                    if(ObjectUtil.isNotNull(transitionPolyline)){
                        //存储过渡段的路线字符串和路线令牌（两点间的路线加密串,如果不为空则存储起来-两点间太近、访问点、最后一个是没有的）
                        tmsVehicleRouteTransitionEntity.setRouteToken(routeToken);
                        tmsVehicleRouteTransitionEntity.setTransitionPolylinePoints(transitionPolyline);
                    }
                    //是否重新进行过路径规划区分标志（0：false，1和其他：true）
                    tmsVehicleRouteTransitionEntity.setReplanningSign(tmsVehicleRouteEntity.getReplanningSign());
                    tmsVehicleRouteTransitionEntities.add(tmsVehicleRouteTransitionEntity);
                }
            }
            //批量保存
            tmsVehicleRouteTransitionService.saveBatch(tmsVehicleRouteTransitionEntities);
            tmsVehicleRouteVisitService.saveBatch(tmsVehicleRouteVisitEntities);
        }
    }

    @Override
    public R getRoutePlanByShipmentNo(String shipmentNo) {
        TmsVehicleRouteEntity tmsVehicleRouteEntity = tmsVehicleRouteService.getOne(new LambdaQueryWrapper<TmsVehicleRouteEntity>().eq(TmsVehicleRouteEntity::getShipmentNo, shipmentNo));
        if(ObjectUtil.isNotNull(tmsVehicleRouteEntity)){
            if(tmsVehicleRouteEntity.getReplanningSign().equals(0)){
                //没有重新规划的取第一次路径规划的串
                return R.ok(tmsVehicleRouteEntity.getRoutePolylinePoints());
            }else{
                //重新规划的取此时最新路径规划的串
                return R.ok(tmsVehicleRouteEntity.getTempRoutePolylinePoints());
            }

        }else{
            return R.failed(LocalizedR.getMessage("route_planning.no_route_planned",null));
        }

    }

    @Override
    public R uploadVehicleLocation(TmsVehicleLocationDto vehicleLocationDto) {
        TmsVehicleRouteEntity tmsVehicleRouteEntity = tmsVehicleRouteService.getOne(new LambdaQueryWrapper<TmsVehicleRouteEntity>().eq(TmsVehicleRouteEntity::getShipmentNo, vehicleLocationDto.getShipmentNo()));
        if(ObjectUtil.isNull(tmsVehicleRouteEntity)){
            return R.failed(LocalizedR.getMessage("route_planning.no_route_planned",null));
        }
        //实时上传的经纬度放到mongo中存储
        TmsRealTimeLocation tmsRealTimeLocation = new TmsRealTimeLocation();
        tmsRealTimeLocation.setVehicleRouteId(tmsVehicleRouteEntity.getVehicleRouteId());
        tmsRealTimeLocation.setLat(vehicleLocationDto.getLatitude());
        tmsRealTimeLocation.setLng(vehicleLocationDto.getLongitude());
        tmsRealTimeLocation.setUploadTime(LocalDateTime.now());
        tmsRealTimeLocationService.save(tmsRealTimeLocation);
        return R.ok(Boolean.TRUE);
    }

    @Override
    public R getLatestLocaltion(List<String> shipmentNos) {
        List<TmsVehicleRouteEntity> tmsVehicleRouteEntitys = tmsVehicleRouteService.list(new LambdaQueryWrapper<TmsVehicleRouteEntity>().in(TmsVehicleRouteEntity::getShipmentNo, shipmentNos));
        List<TmsShipmentOrderEntity> shipmentOrderList = tmsShipmentOrderService.list(new LambdaQueryWrapper<TmsShipmentOrderEntity>().in(TmsShipmentOrderEntity::getShipmentNo, shipmentNos));
        Map<Long, TmsDriverEntity> driverMap= new HashMap<>();
        if(ObjectUtil.isNotNull(shipmentOrderList) && !shipmentOrderList.isEmpty()){
            List<Long> driverIds = shipmentOrderList.stream().map(TmsShipmentOrderEntity::getDriverId).collect(Collectors.toList());
            driverMap = tmsDriverMapper.selectList(new LambdaQueryWrapper<TmsDriverEntity>().in(TmsDriverEntity::getDriverId, driverIds)).stream().collect(Collectors.toMap(TmsDriverEntity::getDriverId, Function.identity()));
        }
        List<Long> vehicleRouteIds = tmsVehicleRouteEntitys.stream().map(TmsVehicleRouteEntity::getVehicleRouteId).collect(Collectors.toList());
        Map<Long, List<TmsVehicleRouteVisitEntity>> tmsVehicleRouteVisitEntityMap = tmsVehicleRouteVisitService.list(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>().in(TmsVehicleRouteVisitEntity::getVehicleRouteId, vehicleRouteIds)).stream().collect(Collectors.groupingBy(TmsVehicleRouteVisitEntity::getVehicleRouteId));
        //根据车辆路线id查询mongo中存储的实时路线经纬度
        List<TmsRealTimeLocation> tmsRealTimeLocations = tmsRealTimeLocationService.list(new QueryWrapper<TmsRealTimeLocation>().in(TmsRealTimeLocation::getVehicleRouteId, vehicleRouteIds));
        Map<Long, List<TmsRealTimeLocation>> tmsRealTimeLocationMap = tmsRealTimeLocations.stream().collect(Collectors.groupingBy(TmsRealTimeLocation::getVehicleRouteId));
        List<VehicleLocaltionPointVo> vehicleLocaltionPointVos = new ArrayList<>();
        if(ObjectUtil.isNotNull(tmsVehicleRouteEntitys) && !tmsVehicleRouteEntitys.isEmpty()){
            for (TmsVehicleRouteEntity tmsVehicleRouteEntity : tmsVehicleRouteEntitys) {
                VehicleLocaltionPointVo vehicleLocaltionPointVo = new VehicleLocaltionPointVo();
                if(ObjectUtil.isNotNull(tmsRealTimeLocationMap.get(tmsVehicleRouteEntity.getVehicleRouteId()))){
                    //按上传时间排序
                    List<TmsRealTimeLocation> tempTmsRealTimeLocations = tmsRealTimeLocationMap.get(tmsVehicleRouteEntity.getVehicleRouteId()).stream()
                            .sorted(Comparator.comparing(TmsRealTimeLocation::getUploadTime, Comparator.reverseOrder())).collect(Collectors.toList());
                    //倒序后取第一个即为最新的经纬度
                    TmsRealTimeLocation tmsRealTimeLocation = tempTmsRealTimeLocations.get(0);
                    vehicleLocaltionPointVo.setLat(tmsRealTimeLocation.getLat());
                    vehicleLocaltionPointVo.setLng(tmsRealTimeLocation.getLng());
                    vehicleLocaltionPointVo.setShipmentNo(tmsVehicleRouteEntity.getShipmentNo());
                    vehicleLocaltionPointVo.setDriverInfo(driverMap.get(Long.parseLong(tmsVehicleRouteEntity.getVehicleLabel())));
                    vehicleLocaltionPointVos.add(vehicleLocaltionPointVo);
                }else{
                    //如果司机还没有实时上传过最新的经纬度（每隔3分钟），则取visit访问顺序的第一个点来暂时表示此时司机的位置
                    List<TmsVehicleRouteVisitEntity> tmsVehicleRouteVisitEntitys = tmsVehicleRouteVisitEntityMap.get(tmsVehicleRouteEntity.getVehicleRouteId()).stream().filter(tmsVehicleRouteVisitEntity -> tmsVehicleRouteVisitEntity.getOrderNum() == 1).collect(Collectors.toList());
                    vehicleLocaltionPointVo.setLat(tmsVehicleRouteVisitEntitys.get(0).getLatitude());
                    vehicleLocaltionPointVo.setLng(tmsVehicleRouteVisitEntitys.get(0).getLongitude());
                    vehicleLocaltionPointVo.setShipmentNo(tmsVehicleRouteEntity.getShipmentNo());
                    vehicleLocaltionPointVo.setDriverInfo(driverMap.get(Long.parseLong(tmsVehicleRouteEntity.getVehicleLabel())));
                    vehicleLocaltionPointVos.add(vehicleLocaltionPointVo);
                }
            }
        }else {
            return R.failed(LocalizedR.getMessage("route_planning.no_routes_planned",null));
        }
        return R.ok(vehicleLocaltionPointVos);
    }

    @Override
    public RoutePointVo getRoutePointByShipmentNo(String shipmentNo) {
        TmsVehicleRouteEntity tmsVehicleRouteEntity = tmsVehicleRouteService.getOne(new LambdaQueryWrapper<TmsVehicleRouteEntity>().eq(TmsVehicleRouteEntity::getShipmentNo, shipmentNo));
        //保存路线的时候，多个派送任务都是同一个提货点的时候会重复，所以这里利用LinkedHashMap和LinkedHashSet唯一性和顺序去重
        // 使用 LinkedHashMap 保证键的唯一性和顺序
        Map<String, VehicleLocaltionPointVo> tempMap = new LinkedHashMap<>();
        Set<VehicleLocaltionPointVo> vehicleLocaltionPointVos = new LinkedHashSet<>();
        RoutePointVo routePointVo = new RoutePointVo();
        if(ObjectUtil.isNotNull(tmsVehicleRouteEntity)){
            //根据谷歌返回的visit顺序排序（确保访问点顺序）
            List<TmsVehicleRouteVisitEntity> list = tmsVehicleRouteVisitService.list(
                    new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                    .eq(TmsVehicleRouteVisitEntity::getVehicleRouteId, tmsVehicleRouteEntity.getVehicleRouteId())
                    .eq(TmsVehicleRouteVisitEntity::getReplanningSign, tmsVehicleRouteEntity.getReplanningSign()))
                    .stream().sorted(Comparator.comparing(TmsVehicleRouteVisitEntity::getOrderNum)).collect(Collectors.toList());
            final String[] finalKey = {""};
            if(ObjectUtil.isNotNull(list) && !list.isEmpty()){
                AtomicInteger keyOrder= new AtomicInteger(1);
                AtomicReference<String> firstKey = new AtomicReference<>("");
                list.forEach(tmsVehicleRouteVisitEntity -> {
                    //遍历此时的路线访问顺序
                    VehicleLocaltionPointVo vehicleLocaltionPointVo = new VehicleLocaltionPointVo();
                    if(ObjectUtil.isNotNull(tmsVehicleRouteVisitEntity.getLatitude()) && ObjectUtil.isNotNull(tmsVehicleRouteVisitEntity.getLongitude())){
                        vehicleLocaltionPointVo.setLat(tmsVehicleRouteVisitEntity.getLatitude());
                        vehicleLocaltionPointVo.setLng(tmsVehicleRouteVisitEntity.getLongitude());
                        vehicleLocaltionPointVo.setIsPickup(tmsVehicleRouteVisitEntity.getIsPickup());
                        //到达该点的时间
                        vehicleLocaltionPointVo.setActualArrivalTime(tmsVehicleRouteVisitEntity.getActualArrivalTime());
                        //将委托单号也携带出来用来给前端判断
                        vehicleLocaltionPointVo.getEntrustedOrderNumbers().addAll(Arrays.asList(tmsVehicleRouteVisitEntity.getShipmentLabel()));
                        vehicleLocaltionPointVo.setShipmentNo(tmsVehicleRouteEntity.getShipmentNo());
                        //经纬度（利用hashMap的key唯一性来确保有顺序的同一个经纬度的多个访问点合为一个提货访问点或者派送访问点）
                        String key = tmsVehicleRouteVisitEntity.getLatitude() + "_" + tmsVehicleRouteVisitEntity.getLongitude();
                        //记录第一个访问点的key（如果是第一个访问点（NB仓库），则需要特殊处理，以便满足揽货需求）
                        if(keyOrder.get() ==1){
                            firstKey.set(key);
                        }
                        // 如果 key 存在，则更新对应的委托单信息（同一地点的多个委托单（取货或者送货）要添加进来--一个点的可能有多个单的取货和派送）
                        if(tempMap.containsKey(key)){
                            //判断是否是第一个访问点（NB仓库）并且是送货点，如果是则需要特殊处理，以便满足揽货需求
                            if(firstKey.get().equals(key) && tmsVehicleRouteVisitEntity.getIsPickup()==0){
                                //如果是第一个访问点（NB仓库）且是送货点，则需要特殊处理,是第一个访问点的经纬度并且是送货点则说明此时这个点是统一揽货返回到NB仓库的情况,将其记录并放到最后面去
                                //揽收终点的新key
                                finalKey[0] = key + "_"+"f";
                                if(tempMap.containsKey(finalKey[0])){
                                    VehicleLocaltionPointVo tempVehicleLocaltionPointVo = tempMap.get(finalKey[0]);
                                    tempVehicleLocaltionPointVo.getEntrustedOrderNumbers().add(tmsVehicleRouteVisitEntity.getShipmentLabel());
                                    tempMap.put(finalKey[0], tempVehicleLocaltionPointVo);
                                }else{
                                    tempMap.put(finalKey[0], vehicleLocaltionPointVo);
                                }
                            }else{
                                //但是同一个经纬度点并且都是提货或者派送的情况-按顺序合并点（因为点之间有顺序）
                                VehicleLocaltionPointVo tempVehicleLocaltionPointVo = tempMap.get(key);
                                tempVehicleLocaltionPointVo.getEntrustedOrderNumbers().add(tmsVehicleRouteVisitEntity.getShipmentLabel());
                                tempMap.put(key, tempVehicleLocaltionPointVo);
                            }
                        }else{
                            // 如果 key 不存在，则顺序添加到 LinkedHashMap
                            tempMap.put(key, vehicleLocaltionPointVo);
                        }
                    }
                    keyOrder.getAndIncrement();

                });
                //保证最后揽完所有货后返回NB仓库是在最后一个点(删除再最后重新插入)
                VehicleLocaltionPointVo remove = tempMap.remove(finalKey[0]);
                if(ObjectUtil.isNotNull(remove)){
                    tempMap.put(finalKey[0], remove);
                }
                // 将 LinkedHashMap 的值按顺序放入 LinkedHashSet
                vehicleLocaltionPointVos.addAll(tempMap.values());
            }
            routePointVo.setVehicleLocaltionPointVos(vehicleLocaltionPointVos);
            //将此时的最新司机所在的目的地经纬度返回，点击了前往下一个目的地（会存储一个经纬度->此时存的就是下一个目的地的经纬度，如果还没有点击前往下一个目的地的话就是当前目的地）--用于前端应用存档（避免异常弹出后无法从当前目的地继续派送）
            if(ObjectUtil.isNotNull(tmsVehicleRouteEntity.getCurrentPointLat()) && ObjectUtil.isNotNull(tmsVehicleRouteEntity.getCurrentPointLng()) ){
                //点击了下一个目的地（上传过此时的经纬度才会有--保存过visit状态）
                routePointVo.setLat(tmsVehicleRouteEntity.getCurrentPointLat());
                routePointVo.setLng(tmsVehicleRouteEntity.getCurrentPointLng());
            }
            else {
                //没有点击下一个目的地（没有上传过此时的经纬度为null）
                routePointVo.setLat(null);
                routePointVo.setLng(null);
            }
            return routePointVo;
        }else{
            throw new CustomBusinessException(412,LocalizedR.getMessage("route_planning.not_planned_yet",null));
        }
    }

    /**
     * 获取地址的经纬度
     * @return
     */
    @SneakyThrows
    @Override
    public String getLatLngByAddress(String address) {
        // 构造 HTTP 客户端
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();

        //对地址进行URL编码-utf-8
        address = URLEncoder.encode(address, "UTF-8");

        String osName = System.getProperty("os.name").toLowerCase();
        // 如果是 dev 环境，设置代理
        if ("dev".equals(activeProfile) || osName.contains("win")) {
            System.out.println("进入设置代理");
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 7890));
            clientBuilder.proxy(proxy);
        }

        OkHttpClient client = clientBuilder.build();

        HttpUrl url = new HttpUrl.Builder()
                .scheme("https")
                .host("maps.googleapis.com")
                .addPathSegment("maps")
                .addPathSegment("api")
                .addPathSegment("geocode")
                .addPathSegment("json")
                .addQueryParameter("address", address)
                .addQueryParameter("key", "AIzaSyAJPHMzFIOVgBiyukRDkG9J91LVEDZvpDk")
                .build();
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
        String locationString="";
        try(Response execute = client.newCall(request).execute()) {
            if (execute.isSuccessful() && execute.body() != null) {
                JSONObject jsonObject = JSON.parseObject(execute.body().string());
                if ("OK".equals(jsonObject.getString("status"))) {
                    // 提取 results 数组
                    JSONArray results = jsonObject.getJSONArray("results");
                    if (results != null && !results.isEmpty()) {
                        // 获取第一个结果对象
                        JSONObject result = results.getJSONObject(0);
                        // 提取 geometry 对象
                        JSONObject geometry = result.getJSONObject("geometry");
                        if (geometry != null) {
                            // 提取 location 对象
                            JSONObject location = geometry.getJSONObject("location");
                            if (location != null) {
                                double lat = location.getDouble("lat");
                                double lng = location.getDouble("lng");
                                locationString = lat + "," + lng;
                            }
                        }
                    }
                }
            } else {
                throw new RuntimeException("谷歌授权令牌获取失败");
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return locationString;
    }


    @SneakyThrows
    @Override
    public GeocodeResult getLatLngByAddress2(String address) {
        GeocodeResult geocodeResult = new GeocodeResult();
        // 构造 HTTP 客户端
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();

        //对地址进行URL编码-utf-8
        address = URLEncoder.encode(address, "UTF-8");

        String osName = System.getProperty("os.name").toLowerCase();
        System.out.println("当前获取到的环境为："+activeProfile);

        // 如果是 dev 环境，设置代理
        if ("dev".equals(activeProfile) || osName.contains("win")) {
            System.out.println("进入设置代理");
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 7890));
            clientBuilder.proxy(proxy);
        }

        OkHttpClient client = clientBuilder.build();

        HttpUrl url = new HttpUrl.Builder()
                .scheme("https")
                .host("maps.googleapis.com")
                .addPathSegment("maps")
                .addPathSegment("api")
                .addPathSegment("geocode")
                .addPathSegment("json")
                .addQueryParameter("address", address)
                .addQueryParameter("key", "AIzaSyAJPHMzFIOVgBiyukRDkG9J91LVEDZvpDk")
                .build();
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
        String locationString="";
        try(Response execute = client.newCall(request).execute()) {
            if (execute.isSuccessful() && execute.body() != null) {
                JSONObject jsonObject = JSON.parseObject(execute.body().string());
                if ("OK".equals(jsonObject.getString("status"))) {
                    // 提取 results 数组
                    JSONArray results = jsonObject.getJSONArray("results");
                    if (results != null && !results.isEmpty()) {
                        // 获取第一个结果对象
                        JSONObject result = results.getJSONObject(0);
                        // 提取 geometry 对象
                        JSONObject geometry = result.getJSONObject("geometry");
                        if (geometry != null) {
                            // 提取 location 对象
                            JSONObject location = geometry.getJSONObject("location");
                            if (location != null) {
                                double lat = location.getDouble("lat");
                                double lng = location.getDouble("lng");
                                locationString = lat + "," + lng;
                            }
                        }

                        // 获取国家
                        JSONArray components = result.getJSONArray("address_components");
                        for (int i = 0; i < components.size(); i++) {
                            JSONObject comp = components.getJSONObject(i);
                            JSONArray types = comp.getJSONArray("types");
                            if (types.contains("country")) {
                                geocodeResult.setCountry(comp.getString("long_name"));
                                break;
                            }
                        }
                    }
                }
            } else {
                throw new RuntimeException("谷歌授权令牌获取失败");
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        geocodeResult.setLocation(locationString);
        return geocodeResult;
    }

    @Override
    public R routeReplanning(TmsRouteReplanningDto tmsRouteReplanningDto, Integer type) {
        //处理委托单数据(只拿主委托单进行路径规划即可，因为子单的地址是跟主单一样的，只是拆分了而已)
        List<TmsEntrustedOrderEntity> tmsEntrustedOrderEntitys = tmsEntrustedOrderService.list(new LambdaQueryWrapper<TmsEntrustedOrderEntity>().eq(TmsEntrustedOrderEntity::getShipmentNo, tmsRouteReplanningDto.getShipmentNo()).eq(TmsEntrustedOrderEntity::getIsSubOrderNo, Boolean.FALSE)).stream().collect(Collectors.toList());
        //过滤
        if(type.equals(1)){
            //跳过后重新规划路线(过滤跳过（已经设置为二次派送状态）的和已完成)
            tmsEntrustedOrderEntitys = tmsEntrustedOrderEntitys.stream().filter(tmsEntrustedOrderEntity -> (!(tmsEntrustedOrderEntity.getOrderStatus().equals(EntrustedOrderStatus.DELIVERED.getCode())))&& (!(tmsEntrustedOrderEntity.getOrderStatus().equals(EntrustedOrderStatus.WAIT_SECOND_DELIVERY.getCode()))) ).collect(Collectors.toList());
        }else{
            //二次派送重新规划路线（过滤已完成的）
            tmsEntrustedOrderEntitys = tmsEntrustedOrderEntitys.stream().filter(tmsEntrustedOrderEntity -> !(tmsEntrustedOrderEntity.getOrderStatus().equals(EntrustedOrderStatus.DELIVERED.getCode()))).collect(Collectors.toList());
        }
        if(ObjectUtil.isNotNull(tmsEntrustedOrderEntitys) && !tmsEntrustedOrderEntitys.isEmpty()){
            //收集委托单的始发地与目的地的经纬度信息
            Map<String, String> orderNumberLatLngMap = new HashMap<>();
            for (TmsEntrustedOrderEntity tmsEntrustedOrderEntity : tmsEntrustedOrderEntitys) {
                String entrustedOrderNumber = tmsEntrustedOrderEntity.getEntrustedOrderNumber();
                String shipperAddress = tmsEntrustedOrderEntity.getShipperLatLng();
                String receiverLatLng = tmsEntrustedOrderEntity.getReceiverLatLng();
                orderNumberLatLngMap.put(entrustedOrderNumber, shipperAddress + "/" + receiverLatLng);
            }
            Map<String, TmsEntrustedOrderEntity> entrustedOrderNumberMap = tmsEntrustedOrderEntitys.stream().collect(Collectors.toMap(TmsEntrustedOrderEntity::getEntrustedOrderNumber, Function.identity()));
            //处理司机车辆信息
            if(ObjectUtil.isNull(tmsRouteReplanningDto.getDriverId())){
                return R.failed(LocalizedR.getMessage("route_planning.driver_not_found",null));
            }
            TmsVehicleDriverRelationEntity tmsVehicleDriverRelation = vehicleDriverRelationMapper.selectOne(new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                    .eq(TmsVehicleDriverRelationEntity::getDriverId, tmsRouteReplanningDto.getDriverId()), false);
            TmsVehicleInfoEntity vehicleInfo= tmsVehicleInfoService.getOne(new LambdaQueryWrapper<TmsVehicleInfoEntity>()
                    .eq(TmsVehicleInfoEntity::getId, tmsVehicleDriverRelation.getVehicleId()));
            if(ObjectUtil.isNull(vehicleInfo)){
                throw  new CustomBusinessException(412,LocalizedR.getMessage("route_planning.vehicle_not_found",null));
            }

            // 构造 HTTP 客户端
            OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();

            // 如果是 dev 环境，设置代理
            if ("dev".equals(activeProfile)) {
                Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 7890));
                clientBuilder.proxy(proxy);
            }

            OkHttpClient client = clientBuilder.build();

            //先获取谷歌地图授权token令牌
            String finalToken = getToken();
            if(StrUtil.isEmpty(finalToken)){
                throw  new CustomBusinessException(500,LocalizedR.getMessage("route_planning.token_obtain_failed",null));
            }
            MediaType mediaType = MediaType.parse("application/json");

            //利用建造者模式构建请求参数对象
            String jsonString = replanningBuildRequest(tmsRouteReplanningDto, orderNumberLatLngMap, entrustedOrderNumberMap, Arrays.asList(vehicleInfo));

            RequestBody body = RequestBody.create(jsonString, mediaType);

            Request request = new Request.Builder()
                    .url("https://routeoptimization.googleapis.com/v1/projects/nbexpress-433910:optimizeTours")
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Authorization", finalToken)
                    .addHeader("Connection", "keep-alive")
                    .build();
            // 发送请求并处理响应
            String responseBody="";
            try (Response response = client.newCall(request).execute()) {
                if(response.code()==401){
                    return R.failed(LocalizedR.getMessage("route_planning.network_fluctuation",null));
                }
                if(response.code()==400){
                    return R.failed(LocalizedR.getMessage("route_planning.invalid_input_parameters",null));
                }
                if (response.isSuccessful() && response.body() != null) {
                    // 解析响应结果，保存路线规划信息
                    parseAndSaveResponse(response, orderNumberLatLngMap,tmsRouteReplanningDto.getShipmentNo(),tmsRouteReplanningDto.getDriverId(),2);
                } else {
                    log.error("谷歌地图路线规划请求失败------------------>: " + response.code()+response.body().string());
                }
            } catch (IOException e) {
                e.printStackTrace();
                return R.failed(LocalizedR.getMessage("route_planning.request_failed",null));
            }
            return R.ok(LocalizedR.getMessage("route_planning.success",null));
        }
        else{
            return R.failed("重新规划失败！");
        }
    }

    @Override
    public R skipOrder(SkipOrderDto skipOrderDto) {
        //主委托单和子单
        List<TmsEntrustedOrderEntity> tmsEntrustedOrderEntitys = tmsEntrustedOrderService.list(new LambdaQueryWrapper<TmsEntrustedOrderEntity>().likeRight(TmsEntrustedOrderEntity::getEntrustedOrderNumber, skipOrderDto.getSkipEntrustedOrderNumber()));
        if(ObjectUtil.isNotNull(tmsEntrustedOrderEntitys) && !tmsEntrustedOrderEntitys.isEmpty()){
            //过滤出主单
            List<TmsEntrustedOrderEntity> tmsEntrustedOrderEntityMain = tmsEntrustedOrderEntitys.stream().filter(item -> item.getIsSubOrderNo().equals(Boolean.FALSE)).collect(Collectors.toList());
            if(EntrustedOrderStatus.PENDING_PICKUP.getCode().equals(tmsEntrustedOrderEntityMain.get(0).getOrderStatus())) {
                //如果此时是待提货状态就修改它为待二次提货状态
                for (TmsEntrustedOrderEntity tmsEntrustedOrderEntity : tmsEntrustedOrderEntitys) {
                    tmsEntrustedOrderEntity.setOrderStatus(EntrustedOrderStatus.PENDING_SECOND_PICKUP.getCode());
                }
                tmsEntrustedOrderService.updateBatchById(tmsEntrustedOrderEntitys);
                return R.ok(LocalizedR.getMessage("route_planning.skip_successful",null));
            }
            if(EntrustedOrderStatus.IN_TRANSIT.getCode().equals(tmsEntrustedOrderEntityMain.get(0).getOrderStatus())) {
                //如果此时是待提货状态就修改它为待二次提货状态
                for (TmsEntrustedOrderEntity tmsEntrustedOrderEntity : tmsEntrustedOrderEntitys) {
                    tmsEntrustedOrderEntity.setOrderStatus(EntrustedOrderStatus.WAIT_SECOND_DELIVERY.getCode());
                }

                tmsEntrustedOrderService.updateBatchById(tmsEntrustedOrderEntitys);
                return R.ok(LocalizedR.getMessage("route_planning.skip_successful",null));
            }
        }else{
            throw  new CustomBusinessException(412,LocalizedR.getMessage("route_planning.entrusted_order_not_found",null));
        }
        return R.ok(LocalizedR.getMessage("route_planning.skip_successful",null));
    }

    @Override
    public R saveVisitStatus(SaveVisitStatusDto saveVisitStatusDto) {
        TmsVehicleRouteEntity one = tmsVehicleRouteService.getOne(new LambdaQueryWrapper<TmsVehicleRouteEntity>().eq(TmsVehicleRouteEntity::getShipmentNo, saveVisitStatusDto.getShipmentNo()));
        if(ObjectUtil.isNotNull(one)){
            one.setCurrentPointLat(saveVisitStatusDto.getLat());
            one.setCurrentPointLng(saveVisitStatusDto.getLng());
        }
        if(tmsVehicleRouteService.updateById(one)){
            return R.ok(LocalizedR.getMessage("route_planning.save_status_successful",null));
        }
        return R.failed(LocalizedR.getMessage("route_planning.save_failed",null));
    }

    @Override
    public R vehicleStopReceiveOrder(Long driverId) {
        TmsShipmentOrderEntity one = tmsShipmentOrderService.getOne(new LambdaQueryWrapper<TmsShipmentOrderEntity>()
                .eq(TmsShipmentOrderEntity::getDriverId, driverId)
                .eq(TmsShipmentOrderEntity::getShipmentStatus, ShipmentOrderEnum.IN_TRANSIT.getType()));
        //移除规划失败的委托单或者派送不完的委托单
        List<TmsEntrustedOrderEntity> tmsEntrustedOrderEntitys = tmsEntrustedOrderService.list(new LambdaQueryWrapper<TmsEntrustedOrderEntity>().eq(TmsEntrustedOrderEntity::getShipmentNo, one.getShipmentNo()));
        if(ObjectUtil.isNotNull(tmsEntrustedOrderEntitys)){
            //过滤出未完成的委托订单
            List<TmsEntrustedOrderEntity> unfinishTmsEntrustedOrderEntityList = tmsEntrustedOrderEntitys.stream().
                    filter(tmsEntrustedOrderEntity -> !(EntrustedOrderStatus.DELIVERED.getCode().equals(tmsEntrustedOrderEntity.getOrderStatus()))).collect(Collectors.toList());
            if(ObjectUtil.isNotNull(unfinishTmsEntrustedOrderEntityList)&& !unfinishTmsEntrustedOrderEntityList.isEmpty()){
                for (TmsEntrustedOrderEntity tmsEntrustedOrderEntity : unfinishTmsEntrustedOrderEntityList) {
                    //如果司机点击停止接单，则将shipmentNo置为null（运输单解绑当前所有未完成的委托单），让其回到调度中心重新指派调度
                    tmsEntrustedOrderEntity.setShipmentNo(null);
                }
            }
            tmsEntrustedOrderService.updateBatchById(unfinishTmsEntrustedOrderEntityList);
            return R.ok(LocalizedR.getMessage("route_planning.stop_receiving_orders_successful",null));
        }
        return R.failed(LocalizedR.getMessage("route_planning.stop_receiving_orders_failed",null));
    }

    @Override
    public R getActualRouteByShipmentNo(String shipmentNo) {
        TmsVehicleRouteEntity tmsVehicleRouteEntity = tmsVehicleRouteService.getOne(new LambdaQueryWrapper<TmsVehicleRouteEntity>().eq(TmsVehicleRouteEntity::getShipmentNo, shipmentNo));
        ActualRouteInfoVo actualRouteInfoVo = new ActualRouteInfoVo();
        List<TmsLatitudeAndLongitudeDto> tmsLatitudeAndLongitudes=new ArrayList<>();
        if(ObjectUtil.isNotNull(tmsVehicleRouteEntity)){
            //如果该路线有实时上传的经纬度串，就从mongo取出来处理返回
            QueryWrapper<TmsRealTimeLocation> wrapper = new QueryWrapper<>();
            wrapper.eq(TmsRealTimeLocation::getVehicleRouteId,tmsVehicleRouteEntity.getVehicleRouteId());
            List<TmsRealTimeLocation> tmsRealTimeLocations = tmsRealTimeLocationService.list(wrapper);
            if(ObjectUtil.isNotNull(tmsRealTimeLocations) && !tmsRealTimeLocations.isEmpty()){
                tmsLatitudeAndLongitudes = tmsRealTimeLocations.stream()
                        .map(location -> {
                            TmsLatitudeAndLongitudeDto dto = new TmsLatitudeAndLongitudeDto();
                            dto.setLatitude(location.getLat());
                            dto.setLongitude(location.getLng());
                            dto.setUploadTime(location.getUploadTime());
                            return dto;
                        })
                        .collect(Collectors.toList());
            }
            //实际路线经纬度点集合
            actualRouteInfoVo.setTmsLatitudeAndLongitudes(tmsLatitudeAndLongitudes);
            //获取规划路线上的取货点和派送点信息集合
            RoutePointVo routePointVo = getRoutePointByShipmentNo(shipmentNo);
            if(ObjectUtil.isNotNull(routePointVo)){
                actualRouteInfoVo.setVehicleLocaltionPointVos(routePointVo.getVehicleLocaltionPointVos());
            }
        }else{
            throw new CustomBusinessException(412,LocalizedR.getMessage("route_planning.shipment_does_not_exist",null));
        }
        return R.ok(actualRouteInfoVo);
    }

    @Override
    public R saveActualArrivalTime(String entrustedOrderNumber, Integer isPickup, LocalDateTime actualArrivalTime) {
        LambdaUpdateWrapper<TmsVehicleRouteVisitEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(TmsVehicleRouteVisitEntity::getShipmentLabel,entrustedOrderNumber)
                .eq(TmsVehicleRouteVisitEntity::getIsPickup,isPickup)
                .set(TmsVehicleRouteVisitEntity::getActualArrivalTime,actualArrivalTime);
        return R.ok(tmsVehicleRouteVisitService.update(wrapper));
    }

    @NotNull
    private  String getToken() {
        String finalToken="";

        // 构造 HTTP 客户端
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();

        // 如果是 dev 环境，设置代理
        if (activeProfile.equals("dev")) {
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 7890));
            clientBuilder.proxy(proxy);
        }

        OkHttpClient client = clientBuilder.build();

        HttpUrl queryTokenUrl= new HttpUrl.Builder()
                .scheme("Https")
                .host("token.great-vision.ca")
                .addQueryParameter("key","JiaYouGoogleToken").build();
        Request tokenRequest = new Request.Builder()
                .url(queryTokenUrl)
                .build();
        // 发送请求并处理响应
        try (Response tokenResponse = client.newCall(tokenRequest).execute()) {
            if (tokenResponse.isSuccessful() && tokenResponse.body() != null) {
                JSONObject jsonObject = JSON.parseObject(tokenResponse.body().string());
                String token = (String)jsonObject.get("token");
                //拼接上Bearer
                finalToken = "Bearer " + token;
            } else {
                throw new RuntimeException("谷歌授权令牌获取失败");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return finalToken;
    }

    /**
     * 中大件派送或者揽收路线规划请求参数构建
     * @param tmsRoutePlanDto
     * @param orderNumberLatLngMap
     * @param entrustedOrderNumberMap
     * @param vehicleInfos
     * @return
     */
    @NotNull
    private String buildLargeAndMediumRoutePlanRequest(TmsRoutePlanDto tmsRoutePlanDto, Map<String, String> orderNumberLatLngMap, Map<String, TmsEntrustedOrderEntity> entrustedOrderNumberMap, List<TmsVehicleInfoEntity> vehicleInfos,String type) {
        OptimizationRequest.Model model = new OptimizationRequest.Model();
        RequestBuilder builder = new RequestBuilder(model,true,true);
        //设置此次路线规划的全局时间限制范围（当前时间往前后波动2天，避免时区不同导致超出了全局时间范围）
        LocalDate now = LocalDate.now();
        LocalDateTime globalStartTime = LocalDateTime.of(now.minusDays(2), LocalTime.of(0, 0, 0));
        LocalDateTime globalEndTime = LocalDateTime.of(now.plusDays(2), LocalTime.of(23, 59, 59));
        //均使用UTC时间格式（谷歌地图要求UTC格式的时间）
        String globalStartTimeUTCString = globalStartTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
        String globalEndTimeUTCString = globalEndTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
//        builder.withGlobalTime("2025-03-19T07:00:00Z", "2025-03-20T06:59:00Z");
        builder.withGlobalTime(globalStartTimeUTCString, globalEndTimeUTCString);
        //根据委托单个数设置派送任务Shipments
        orderNumberLatLngMap.keySet().forEach(entrustedOrderNumber -> {
            //处理从委托单里拿出来的(取货送货点)经纬度
            String latAndLngString = orderNumberLatLngMap.get(entrustedOrderNumber);
            TmsEntrustedOrderEntity tmsEntrustedOrderEntity = entrustedOrderNumberMap.get(entrustedOrderNumber);
            //取货送货点经纬度字符串（24.33211,114.54322/24.44431,114.84322）
            String[] split = latAndLngString.split("/");
            //取货点经纬度字符串（24.33211,114.54322）
            String pickupLatLng = split[0];
            String[] pickupLatLngArr = pickupLatLng.split(",");
            //送货点经纬度字符串（24.44431,114.84322）
            String deliveryLatLng = split[1];
            String[] deliveryLatLngArr = deliveryLatLng.split(",");
            //既然安排到今天，则不管你的派送日期是什么都取今天的日期，时间则取客户下单时定的时间段，保证路线规划不会因日期范围而路径规划失败的情况
            //由于一加时间条件就路径规划失败，所以这里先把时间条件去掉（暂留）
            LocalDate startDate = LocalDate.now();
            LocalDate endDate = LocalDate.now();
            LocalDateTime pickupStartDateTime = LocalDateTime.of(startDate, tmsEntrustedOrderEntity.getEstimatedShippingTimeStart().toLocalTime());
            LocalDateTime pickupendDateTime = LocalDateTime.of(endDate, tmsEntrustedOrderEntity.getEstimatedShippingTimeEnd().toLocalTime());
            LocalDateTime deliveryStartDateTime = LocalDateTime.of(startDate, tmsEntrustedOrderEntity.getEstimatedArrivalTimeStart().toLocalTime());
            LocalDateTime deliveryEndDateTime = LocalDateTime.of(endDate, tmsEntrustedOrderEntity.getEstimatedArrivalTimeEnd().toLocalTime());
            //根据委托单构建shipment运输任务对象（请求参数）
            builder .addShipment(shipment -> shipment
                            //取货点条件参数
//                            .withPickup(pickup -> pickup
//                                            //取货点的经纬度
//                                            .withLocation(Double.parseDouble(pickupLatLngArr[0]), Double.parseDouble(pickupLatLngArr[1]))
//                                            //取货点所需大概时间（因没有限定，这里暂用10分钟）
//                                            .withDuration("600s")
////                            .addTimeWindow(pickupStartDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT), pickupendDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT))
//                            )
                            //送货点条件参数
                            .withDelivery(delivery -> delivery
                                            //送货点的经纬度
                                            .withLocation(new BigDecimal(deliveryLatLngArr[0]), new BigDecimal(deliveryLatLngArr[1]))
                                            //送货点所需大概时间（因没有限定，这里也暂用10分钟）
                                            .withDuration("600s")
//                            .addTimeWindow(deliveryStartDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT), deliveryEndDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT))
                            )
                            //此运输任务的载重
                            .withLoadDemand(tmsEntrustedOrderEntity.getTotalWeight().intValue())
                            //委托单号作为label唯一标记
                            .withLabel(entrustedOrderNumber)
            );
        });
        //设置车辆（司机）Vehicles
        Map<Long, TmsCarrierEntity> carrierMap=new HashMap<>();
        //承运商信息
        List<Long> carrierIds = vehicleInfos.stream().map(TmsVehicleInfoEntity::getCarrierId).collect(Collectors.toList());
        if(ObjectUtil.isNotNull(carrierIds) && !carrierIds.isEmpty()){
            carrierMap  = tmsCarrierService.list(new LambdaQueryWrapper<TmsCarrierEntity>()
                            .in(TmsCarrierEntity::getCarrierId, carrierIds)).stream()
                    .collect(Collectors.toMap(TmsCarrierEntity::getCarrierId,Function.identity()));
        }
        //预留多个司机的情况（预留后期扩展）
        for (TmsVehicleInfoEntity vehicleInfo : vehicleInfos) {
            TmsCarrierEntity carrierEntity=null;
            if(ObjectUtil.isNotNull(carrierMap.get(vehicleInfo.getCarrierId()))){
                carrierEntity = carrierMap.get(vehicleInfo.getCarrierId());
            }
            String vehicleWorkStartTime;
            String vehicleWorkEndTime;
            if(ObjectUtil.isNotNull(carrierEntity) && ObjectUtil.isNotNull(carrierEntity.getOpeningTime()) && ObjectUtil.isNotNull(carrierEntity.getClosingTime())){
                //统统取今天的时间（日期取今天，时间段取司机工作时间）
                LocalDate startDate = LocalDate.now();
                LocalDate endDate = LocalDate.now();
                LocalDateTime startDateTime = LocalDateTime.of(startDate, carrierEntity.getOpeningTime());
                LocalDateTime endDateTime = LocalDateTime.of(endDate, carrierEntity.getClosingTime());
                vehicleWorkStartTime = startDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
                vehicleWorkEndTime = endDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
            } else {
                vehicleWorkEndTime = null;
                vehicleWorkStartTime = null;
            }
            //构建谷歌路线规划的司机车辆信息（请求参数）
            builder .addVehicle(vehicle -> vehicle
                            .withStartLocation(tmsRoutePlanDto.getDriverLat(), tmsRoutePlanDto.getDriverLng())
                            //应需求，先暂时无限大（1000吨---货物是kg，车辆10000吨，相当于不加载重参数，预留，防止后期扩展需要）
                            .withLoadLimit(new BigDecimal(10000).multiply(new BigDecimal(1000)).intValue())
                            //todo 后期加上司机的时间窗口限制
//                    .withTimeWindows(vehicleWorkStartTime, vehicleWorkEndTime)
                            //车辆司机id作为label唯一标记
                            .withLabel(vehicleInfo.getDriverId().toString())
                            //每公里成本(由于车辆信息没有所以先暂时固定)
                            .withCostPerKilometer(6)
            );
        }
        //利用build模式创建谷歌路线规划请求参数
        OptimizationRequest optimizationRequest = builder.build();

        return JSON.toJSONString(optimizationRequest);
    }

}
