package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.entity.TmsStoreProfitConfigDetailEntity;

import java.util.List;
import java.util.Map;

public interface TmsStoreProfitConfigDetailService extends IService<TmsStoreProfitConfigDetailEntity> {

    /**
     * 获取规则明细-分组
     * key: profitId
     * value: List<TmsStoreProfitConfigDetailEntity>
     * @param profitIds
     * @return
     */
    Map<Long, List<TmsStoreProfitConfigDetailEntity>> getGroupByProfitIds(List<Long> profitIds);
}
