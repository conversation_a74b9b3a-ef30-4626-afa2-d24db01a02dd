package com.jygjexp.jynx.tms.utils;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class CanadaPostApiExample {
    public static void main(String[] args) {
        OkHttpClient client = new OkHttpClient();

        String url = "https://ws1.postescanada-canadapost.ca/Capture/Interactive/Find/v1.00/json3ex.ws" +
                "?Key=EA98-JC42-TF94-JK98" +
                "&Text=AS" +
                "&Container=" +
                "&Origin=CAN" +
                "&Countries=CAN" +
                "&Datasets=" +
                "&Limit=50" +
                "&Filter=" +
                "&Language=en" +
                "&$block=true" +
                "&$cache=true" +
                "&SOURCE=PCA-SCRIPT";

        Request request = new Request.Builder()
                .url(url)
                .addHeader("origin", "https://www.canadapost-postescanada.ca")
                .addHeader("referer", "https://www.canadapost-postescanada.ca/cpc/en/tools/find-a-postal-code.page")
                .addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.body() != null) {
                String json = response.body().string();
                System.out.println(json);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
